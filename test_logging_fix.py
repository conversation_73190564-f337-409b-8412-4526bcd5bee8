#!/usr/bin/env python3
"""
测试日志配置修复的脚本
"""
import os
import sys

# 模拟容器模式
os.environ['IS_CONTAINER_MODE'] = 'True'

# 模拟原始日志配置
LOGGING = {
    "loggers": {
        "celery": {"handlers": ["null"], "level": "WARNING", "propagate": True},
        "django": {"handlers": ["null"], "level": "INFO", "propagate": True},
        "django.db.backends": {"handlers": ["wb_mysql", "error"], "level": "INFO", "propagate": True},
        "django.request": {"handlers": ["console", "error"], "level": "ERROR", "propagate": True},
        "monitor_web": {"handlers": ["root", "error"], "level": "INFO", "propagate": True},
        "monitor_api": {"handlers": ["root", "error"], "level": "INFO", "propagate": True},
        "bkmonitor": {"handlers": ["root", "error"], "level": "INFO", "propagate": True},
    }
}

print("原始日志配置:")
for logger, config in LOGGING["loggers"].items():
    print(f"  {logger}: {config['handlers']}")

# 应用修复逻辑
IS_CONTAINER_MODE = True

if IS_CONTAINER_MODE:
    for logger in LOGGING["loggers"]:
        if "null" not in LOGGING["loggers"][logger]["handlers"]:
            # 在容器模式下将文件handler替换为console handler，保留error handler
            current_handlers = LOGGING["loggers"][logger]["handlers"]
            if isinstance(current_handlers, list):
                # 将root等文件handler替换为console，保留error handler
                new_handlers = []
                # 如果原来有console，保留console
                if "console" in current_handlers:
                    new_handlers.append("console")
                # 如果原来有root或其他文件handler，替换为console
                elif any(handler in current_handlers for handler in ["root", "component", "wb_mysql"]):
                    new_handlers.append("console")
                # 保留error handler
                if "error" in current_handlers:
                    new_handlers.append("error")
                # 确保至少有console handler
                if not new_handlers:
                    new_handlers = ["console"]
                LOGGING["loggers"][logger]["handlers"] = new_handlers
            else:
                LOGGING["loggers"][logger]["handlers"] = ["console"]

print("\n修复后的日志配置:")
for logger, config in LOGGING["loggers"].items():
    print(f"  {logger}: {config['handlers']}")

print("\n分析:")
print("- celery和django使用null handler，不受影响")
print("- django.request原本有console，保留console和error")
print("- 其他logger原本有root，现在替换为console并保留error")
print("- 这样所有正常日志都会输出到控制台，错误日志同时输出到控制台和error handler")
