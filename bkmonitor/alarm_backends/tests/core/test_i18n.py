# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""


import pytest
from django.utils import timezone, translation

from alarm_backends.core.i18n import i18n
from api.cmdb.define import Business

_business = Business(bk_biz_id=2, bk_biz_name="蓝鲸", time_zone="Asia/Tokyo", language="2")


@pytest.fixture
def business(mocker):
    mocker.patch("alarm_backends.core.cache.cmdb.BusinessManager.get", return_value=_business)


class TestI18n(object):
    def test_set_biz(self, business):
        i18n.set_biz(None)
        assert i18n.get_locale() == "zh_Hans"
        assert i18n.get_timezone() == "Asia/Shanghai"

        assert translation.ugettext("蓝鲸") == "蓝鲸"

        i18n.set_biz(2)
        assert i18n.get_locale() == "en"
        assert i18n.get_timezone() == "Asia/Tokyo"
        assert translation.get_language() == "en"
        assert timezone.get_current_timezone_name() == "Asia/Tokyo"

        assert translation.ugettext("蓝鲸") == "BK"

        _business.language = "1"
        i18n.set_biz(2)
