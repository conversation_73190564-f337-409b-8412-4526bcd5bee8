# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""


from .alert import AccessAlertProcess
from .data import AccessDataProcess, AccessRealTimeDataProcess
from alarm_backends.service.access.event.processor import AccessCustomEventGlobalProcess


class AccessType(object):
    Data = "data"
    RealTimeData = "real_time_data"
    Alert = "alert"
    Event = "event"


ACCESS_TYPE_TO_CLASS = {
    AccessType.Data: AccessDataProcess,
    AccessType.RealTimeData: AccessRealTimeDataProcess,
    AccessType.Alert: AccessAlertProcess,
    AccessType.Event: AccessCustomEventGlobalProcess,  # no use
}
