# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""
import logging
from typing import List

from alarm_backends.core.alert import Alert

logger = logging.getLogger("alert.manager")


class BaseChecker:
    def __init__(self, alerts: List[Alert]):
        self.alerts = alerts

    def is_enabled(self, alert: Alert):
        return alert.is_abnormal()

    def check(self, alert: Alert):
        raise NotImplementedError

    def check_all(self):
        success = 0
        failed = 0
        for alert in self.alerts:
            if self.is_enabled(alert):
                try:
                    self.check(alert)
                    success += 1
                except Exception as e:
                    logger.exception("alert(%s) run checker(%s) failed: %s", alert.id, self.__class__.__name__, e)
                    failed += 1
        logger.info("AlertChecker(%s) run finished, success(%s), failed(%s)", self.__class__.__name__, success, failed)
