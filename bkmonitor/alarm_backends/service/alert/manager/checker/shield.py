# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""
import logging
from typing import List

from alarm_backends.core.alert import Alert
from alarm_backends.service.alert.manager.checker.base import <PERSON><PERSON><PERSON><PERSON>
from alarm_backends.service.converge.shield.shielder import AlertShieldConfigShielder
from alarm_backends.service.fta_action.tasks import create_actions
from bkmonitor.documents import AlertLog
from bkmonitor.models import ActionInstance
from constants.action import ActionPluginType
from constants.alert import EventStatus

logger = logging.getLogger("alert.manager")


class Shield<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(BaseChecker):
    """
    屏蔽状态检测
    """

    def __init__(self, alerts: List[Alert]):
        super().__init__(alerts)
        self.unshielded_actions = []
        self.need_notify_alerts = []
        self.alerts_dict = {alert.id: alert for alert in self.alerts}

    def check_all(self):
        success = 0
        failed = 0
        for alert in self.alerts:
            if self.is_enabled(alert):
                try:
                    self.check(alert)
                    success += 1
                except Exception as e:
                    logger.exception("alert(%s) run checker(%s) failed: %s", alert.id, self.__class__.__name__, e)
                    failed += 1
        logger.info("AlertChecker(%s) run finished, success(%s), failed(%s)", self.__class__.__name__, success, failed)

        if self.unshielded_actions:
            self.push_actions()

    def add_unshield_action(self, alert_doc, relation_id):
        # 存在通知发送的时候，才创建通知动作
        logger.info("ready to push unshielded action for alert(%s)" % alert_doc.id)
        self.unshielded_actions.append(
            {
                "strategy_id": alert_doc.strategy_id,
                "signal": alert_doc.status.lower(),
                "alert_ids": [alert_doc.id],
                "severity": alert_doc.severity,
                "relation_id": relation_id,
                "is_unshielded": True,
            }
        )
        self.need_notify_alerts.append(alert_doc.id)

    def check(self, alert: Alert):
        alert_doc = alert.to_document()
        shielder = AlertShieldConfigShielder(alert_doc)
        shield_result = shielder.is_matched()
        if (
            shield_result is False
            and alert_doc.is_shielded == shield_result
            and not alert.get_extra_info("need_unshield_notice")
        ):
            # 如果当前是否屏蔽状态与DB未屏蔽保持一致，或则不需要更新
            return

        if shield_result is False and alert_doc.status == EventStatus.ABNORMAL and alert_doc.strategy:
            # 解屏蔽之后， 如果告警明确处在异常期，需要直接进行通知发送（即未恢复期间不通知）
            need_unshield_notice = False
            notice_relation = alert_doc.strategy.get("notice", {})
            if alert_doc.is_shielded:
                if alert_doc.status_detail == EventStatus.RECOVERING:
                    logger.info(
                        "ignore push unshielded action for alert(%s) because of alert is in recovering period",
                        alert_doc.id,
                    )
                    alert.update_extra_info("ignore_unshield_notice", True)
                else:
                    # 如果是解除屏蔽 或者是解除屏蔽之后
                    need_unshield_notice = True
            elif alert.get_extra_info("need_unshield_notice"):
                need_unshield_notice = True
                alert.update_extra_info("need_unshield_notice", False)
            if need_unshield_notice and notice_relation:
                self.add_unshield_action(alert_doc, notice_relation["id"])

        shield_left_time = shielder.get_shield_left_time()
        shield_ids = shielder.list_shield_ids()
        alert.set("shield_id", shield_ids)
        alert.set("is_shielded", shield_result)
        alert.set("shield_left_time", shield_left_time)

    def push_actions(self):
        """
        重新推送action
        :return:
        """
        # 需要通知的告警
        need_notify_alerts = []

        # 需要推送的事件
        new_actions = []

        # QOS被放弃执行的数量
        qos_actions = 0

        # DB中已有的事件
        existed_shielded_actions = {}

        # 不需要发送通知的
        noticed_alerts = []

        # 被qos的告警
        qos_alerts = []

        current_count = 0

        # step 1 查找在屏蔽期间发送通知的处理记录
        for action in ActionInstance.objects.filter(need_poll=True, is_polled=False, is_parent_action=True).only(
            "id", "alerts", "inputs", "action_plugin"
        ):
            if (
                set(action.alerts).issubset(set(self.need_notify_alerts))
                and action.action_plugin["plugin_type"] == ActionPluginType.NOTICE
            ):
                # 对应告警的通知将去掉轮询的属性
                if action.inputs.get("is_alert_shielded"):
                    existed_shielded_actions.update({action.id: action.alerts[0]})
                else:
                    noticed_alerts.append(action.alerts[0])

        # step 1 确定有哪些需要发送的事件
        for action in self.unshielded_actions:
            alert_id = action["alert_ids"][0]
            if alert_id in noticed_alerts:
                # 如果当前告警ID存在发送过通知的有效周期任务，直接返回
                continue

            try:
                # 限流计数器，监控的告警以策略ID，信号，告警级别作为维度
                alert = self.alerts_dict.get(alert_id)
                is_qos, current_count = alert.qos_calc(action["signal"])
                if alert_id in existed_shielded_actions.values():
                    # 如果有被屏蔽的通知，解屏蔽之后一定发送
                    new_actions.append(action)
                    need_notify_alerts.append(alert_id)
                elif not is_qos:
                    # 没有过通知处理的，需要QOS限流
                    new_actions.append(action)
                    need_notify_alerts.append(alert_id)
                else:
                    # 达到阈值之后，触发流控
                    qos_actions += 1
                    qos_alerts.append(alert_id)
                    logger.info(
                        "unshielded alert(%s) qos triggered->alert_name(%s)->strategy(%s)-signal(%s)"
                        "-severity(%s)-relation_id(%s),"
                        " current_count->(%s)",
                        alert_id,
                        alert.alert_name,
                        action["strategy_id"],
                        action["signal"],
                        action["severity"],
                        action["relation_id"],
                        current_count,
                    )
            except BaseException as error:
                logger.exception("alert(%s) detect finished, but push actions failed, reason: %s", alert_id, error)

        if qos_alerts:
            # 如果有被qos的事件， 进行日志记录
            qos_log = Alert.create_qos_log(qos_alerts, current_count, qos_actions)
            AlertLog.bulk_create([qos_log])

        need_notify_alerts = set(need_notify_alerts)

        if existed_shielded_actions:
            # 当存在历史的通知记录情况，直接忽略
            logger.info("clear history actions(%s) for unshielded alerts", len(existed_shielded_actions.keys()))
            ActionInstance.objects.filter(id__in=list(existed_shielded_actions.keys())).update(need_poll=False)

        # step 3 推送事件
        for action in new_actions:
            create_actions.delay(**action)

        logger.info(
            "push action for unshielded alerts finished, push (%s) actions, qos (%s) actions",
            len(need_notify_alerts),
            qos_actions,
        )
