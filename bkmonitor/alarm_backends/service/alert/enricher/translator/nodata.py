# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""


from alarm_backends.constants import NO_DATA_TAG_DIMENSION
from alarm_backends.service.alert.enricher.translator.base import BaseTranslator


class NodataTranslator(BaseTranslator):
    """
    :summary 无数据告警翻译去掉标记维度
    """

    def is_enabled(self):
        return True

    def translate(self, data):
        data.pop(NO_DATA_TAG_DIMENSION, None)
        return data
