# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""
import json
import logging
import signal
import threading
import time
from collections import defaultdict
from typing import Dict

from django.conf import settings
from kafka import KafkaConsumer, TopicPartition

from alarm_backends.core.cache.key import (
    ALERT_DATA_POLLER_LEADER_KEY,
    ALERT_HOST_DATA_ID_KEY,
)
from alarm_backends.core.handlers import base
from alarm_backends.management.hashring import HashRing
from alarm_backends.management.utils import get_host_addr
from alarm_backends.service.alert.builder.tasks import run_alert_builder
from bkmonitor.models import EventPluginInstance
from bkmonitor.utils.consul import BKConsul
from bkmonitor.utils.thread_backend import InheritParentThread
from core.drf_resource import api

logger = logging.getLogger("alert.poller")


def always_retry(wait):
    def decorator(func):
        def wrapper(*args, **kwargs):
            while True:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.exception(f"alert handler error: {func.__name__}: {e}")
                    time.sleep(wait)

        return wrapper

    return decorator


class AlertHandler(base.BaseHandler):
    # 内置 topic
    INTERNAL_TOPICS = (settings.MONITOR_EVENT_KAFKA_TOPIC,)  # 蓝鲸监控专用
    MAX_RETRIEVE_NUMBER = 5000
    MAX_EVENT_NUMBER = 500
    MAX_POLLER_THREAD = 20
    _kafka_queues = {}

    def __init__(self, service, *args, **kwargs):
        super(AlertHandler, self).__init__()
        self.service = service
        self.run_once = True
        self._stop_signal = False
        self.topic_data_id = {}
        self.max_event_number = getattr(settings, "MAX_BUILD_EVENT_NUMBER", 0) or self.MAX_EVENT_NUMBER
        self.consumers: Dict[str, KafkaConsumer] = {}
        self.ip = get_host_addr()
        self.redis_client = ALERT_DATA_POLLER_LEADER_KEY.client
        self.data_id_cache_key = ALERT_HOST_DATA_ID_KEY.get_key()
        self.leader_key = ALERT_DATA_POLLER_LEADER_KEY.get_key()
        self.consumers_lock = threading.Lock()

    def _stop(self, *args, **kwargs):
        self._stop_signal = True

    @staticmethod
    def get_all_hosts():
        """
        获取所有运行中机器
        """
        prefix = "{}_{}_{}/{}".format(
            settings.APP_CODE, settings.PLATFORM, settings.ENVIRONMENT, "run_discovery_service-alert"
        )
        client = BKConsul()
        host_keys = client.kv.get(prefix, keys=True)[1]
        return [host_key.split("/")[-2] for host_key in host_keys]

    def handle(self):
        logger.debug("[handle] Starting alert handler initialization")
        # 需要拉取的topic分两部分
        # 1. 内置topic，直接将事件写入到 kafka 中
        # 2. 自定义topic，通过常规事件源接入
        signal.signal(signal.SIGTERM, self._stop)
        signal.signal(signal.SIGINT, self._stop)
        
        logger.debug("[handle] Creating threads")
        leader = InheritParentThread(target=self.run_leader)
        consumer_manager = InheritParentThread(target=self.run_consumer_manager)
        poller = InheritParentThread(target=self.run_poller)
        
        logger.debug("[handle] Starting leader thread")
        leader.start()
        logger.debug("[handle] Starting consumer_manager thread")
        consumer_manager.start()
        logger.debug("[handle] Starting poller thread")
        poller.start()

        try:
            while True:
                try:
                    logger.debug("[handle] Registering service")
                    self.service.register()
                except Exception as error:
                    logger.exception("[main poller thread] register service failed, retry later, error info %s", str(error))
                
                if self._stop_signal:
                    logger.debug("[handle] Stop signal received, joining threads")
                    leader.join()
                    consumer_manager.join()
                    poller.join()
                    logger.debug("[handle] Unregistering service")
                    self.service.unregister()
                    break

                time.sleep(15)
        except Exception as e:
            logger.exception("Do event poller task in host(%s) failed %s", self.ip, str(e))
        finally:
            logger.debug("[handle] Closing all consumers")
            map(lambda c: self.close_consumer(c), self.consumers.values())

    @always_retry(10)
    def run_leader(self):
        logger.debug("[run_leader] Starting leader process")
        # 抢占redis leader锁
        while True:
            logger.debug("[run_leader] Attempting to acquire leader lock")
            result = self.redis_client.set(self.leader_key, self.ip, nx=True, ex=ALERT_DATA_POLLER_LEADER_KEY.ttl)
            leader_ip = self.redis_client.get(self.leader_key)
            if not result and leader_ip != self.ip:
                logger.info(
                    "[run_leader] %s is elected to be alert poller leader already, current host sleep 10 secs",
                    leader_ip,
                )
                time.sleep(10)
            else:
                # leader 分配data_id
                plugin_data_ids = list(
                    EventPluginInstance.objects.filter(is_enabled=True).values_list("data_id", flat=True)
                )
                logger.info(
                    "[run_leader] ip(%s) is elected to be leader, start to dispatch data_ids, %s",
                    self.ip,
                    plugin_data_ids,
                )

                plugin_kafka_configs = defaultdict(list)
                plugin_data_ids.append(0)
                existed_data_kfk_info = {}
                for topics in self.redis_client.hgetall(self.data_id_cache_key).values():
                    for topic_info in json.loads(topics):
                        existed_data_kfk_info[topic_info["data_id"]] = {
                            "topic": topic_info["topic"],
                            "bootstrap_server": topic_info["bootstrap_server"],
                        }

                consumers = {}
                for data_id in plugin_data_ids:
                    try:
                        # 告警默认采用
                        # TODO 是否需要判断data_id是否已经该存在
                        if data_id != 0:
                            if data_id in existed_data_kfk_info:
                                # 增加是否已经分配到了对应的kfk信息
                                bootstrap_server = existed_data_kfk_info[data_id]["bootstrap_server"]
                                topic = existed_data_kfk_info[data_id]["topic"]
                            else:
                                data_id_info = api.metadata.get_data_id(bk_data_id=data_id)
                                kafka_config = data_id_info["result_table_list"][0]["shipper_list"][0]
                                cluster_config = kafka_config["cluster_config"]
                                bootstrap_server = f'{cluster_config["domain_name"]}:{cluster_config["port"]}'
                                topic = kafka_config["storage_config"]["topic"]
                        else:
                            # 使用专用kafka集群: KAFKA_HOST  KAFKA_PORT
                            bootstrap_server = f"{settings.KAFKA_HOST[0]}:{settings.KAFKA_PORT}"
                            # 默认集群使用默认topic，其他集群使用集群名作为topic后缀
                            topic = settings.MONITOR_EVENT_KAFKA_TOPIC

                        if bootstrap_server not in consumers:
                            consumers[bootstrap_server] = KafkaConsumer(bootstrap_servers=bootstrap_server)
                            consumers[bootstrap_server].topics()
                        consumer = consumers[bootstrap_server]

                        partition_configs = []
                        partitions = consumer.partitions_for_topic(topic) or {0}
                        for partition in partitions:
                            # 根据topic的partition进行分配
                            partition_configs.append(
                                {
                                    "partition": partition,
                                    "data_id": data_id,
                                    "topic": topic,
                                    "bootstrap_server": bootstrap_server,
                                }
                            )
                        plugin_kafka_configs[data_id] = partition_configs
                    except Exception as e:
                        logger.exception("get topic info of data id(%s) failed: %s", data_id, e)
                        continue
                try:
                    hosts = self.get_all_hosts()
                except Exception as error:
                    logger.exception("get all host from consul error %s", str(error))
                    hosts = []
                if not hosts:
                    logger.debug("[run_leader] No hosts found, possibly consul service issue, waiting 15 seconds")
                    time.sleep(15)
                else:
                    logger.debug(f"[run_leader] Found {len(hosts)} active hosts: {hosts}")
                    
                    # 创建hash环
                    hash_ring = HashRing({host: 1 for host in hosts})
                    logger.debug("[run_leader] Created hash ring for hosts distribution")
                    
                    host_kfk_info = defaultdict(list)
                    logger.debug(f"[run_leader] Starting to distribute {len(plugin_kafka_configs)} data_ids across hosts")
                    
                    # 分配数据
                    for data_id, kfk_info in plugin_kafka_configs.items():
                        logger.debug(f"[run_leader] Processing data_id: {data_id} with {len(kfk_info)} partitions")
                        for partition_info in kfk_info:
                            partition_key = f"{data_id}|{partition_info['partition']}"
                            host = hash_ring.get_node(partition_key)
                            host_kfk_info[host].append(partition_info)
                            logger.debug(f"[run_leader] Assigned {partition_key} to host: {host}")

                    # Redis操作
                    logger.debug("[run_leader] Starting Redis pipeline operations")
                    pipeline = self.redis_client.pipeline()
                    
                    logger.debug(f"[run_leader] Deleting existing cache key: {self.data_id_cache_key}")
                    self.redis_client.delete(self.data_id_cache_key)
                    
                    # 准备写入数据
                    distribution_data = {host: json.dumps(host_kfk_info[host]) for host in hosts}
                    logger.debug(f"[run_leader] Distribution summary:")
                    for host, info in distribution_data.items():
                        logger.debug(f"[run_leader] Host {host} assigned {len(json.loads(info))} configurations")
                    
                    # 写入新数据
                    logger.debug("[run_leader] Writing new distribution data to Redis")
                    self.redis_client.hmset(
                        self.data_id_cache_key,
                        mapping=distribution_data
                    )
                    
                    # 设置过期时间
                    logger.debug(f"[run_leader] Setting TTL for cache key: {ALERT_HOST_DATA_ID_KEY.ttl} seconds")
                    self.redis_client.expire(self.data_id_cache_key, ALERT_HOST_DATA_ID_KEY.ttl)
                    logger.debug(f"[run_leader] Setting TTL for leader key: {ALERT_DATA_POLLER_LEADER_KEY.ttl} seconds")
                    self.redis_client.expire(self.leader_key, ALERT_DATA_POLLER_LEADER_KEY.ttl)
                    
                    # 执行所有Redis操作
                    logger.debug("[run_leader] Executing Redis pipeline")
                    pipeline.execute()

                    logger.debug("[run_leader] Distribution completed, sleeping for 10 seconds")
                    time.sleep(10)

            if self.run_once or self._stop_signal:
                logger.info(
                    "[run_leader] alert event run leader got stop signal %s, ready to delete leader ip(%s)",
                    self._stop_signal,
                    leader_ip,
                )
                if leader_ip == self.ip and self._stop_signal:
                    # 当前主机为leader并且终止程序之后，直接删除leader缓存
                    logger.info("[run_leader] delete leader cache(%s) by ip(%s)", leader_ip, self.ip)
                    self.redis_client.delete(self.leader_key)
                break

    @always_retry(10)
    def run_consumer_manager(self):
        logger.debug("[run_consumer_manager] Starting consumer manager")
        if self.consumers_lock.locked():
            logger.debug("[run_consumer_manager] Found locked consumer lock, releasing it")
            self.consumers_lock.release()
            logger.debug("[run_consumer_manager] Consumer lock released")
        
        while True:
            logger.debug("[run_consumer_manager] Starting new consumer management cycle")
            logger.debug("[run_consumer_manager] Fetching latest topic information from Redis")
            kfk_confs = json.loads(self.redis_client.hget(self.data_id_cache_key, self.ip) or "{}")

            # kafka集群及所属topic分组
            bootstrap_servers_topics = defaultdict(set)
            logger.debug("[run_consumer_manager] Processing kafka configurations")
            
            for kfk_conf in kfk_confs:
                bootstrap_server = kfk_conf.get("bootstrap_server")
                topic = kfk_conf.get("topic")
                data_id = kfk_conf.get("data_id")
                partition = kfk_conf.get("partition", 0)
                logger.debug(f"[run_consumer_manager] Processing config - server: {bootstrap_server}, topic: {topic}, data_id: {data_id}, partition: {partition}")
                
                if bootstrap_server and topic:
                    self.topic_data_id[f"{bootstrap_server}|{topic}"] = data_id
                    tp = TopicPartition(topic=topic, partition=partition)
                    bootstrap_servers_topics[bootstrap_server].add(tp)
                    logger.debug(f"[run_consumer_manager] Added TopicPartition {tp} to {bootstrap_server}")

            update_bootstrap_servers = []
            create_bootstrap_servers = []
            delete_bootstrap_servers = []
            
            logger.debug("[run_consumer_manager] Analyzing consumer changes")
            logger.debug(f"[run_consumer_manager] Current consumers: {list(self.consumers.keys())}")
            logger.debug(f"[run_consumer_manager] Required servers: {list(bootstrap_servers_topics.keys())}")

            # 检查需要创建或更新的消费者
            for bootstrap_server, tps in bootstrap_servers_topics.items():
                if bootstrap_server not in self.consumers:
                    logger.debug(f"[run_consumer_manager] New server detected: {bootstrap_server}")
                    create_bootstrap_servers.append(bootstrap_server)
                    continue

                consumer = self.consumers[bootstrap_server]
                current_assignment = consumer.assignment()
                logger.debug(f"[run_consumer_manager] Checking assignments for {bootstrap_server}")
                logger.debug(f"[run_consumer_manager] Current assignment: {current_assignment}")
                logger.debug(f"[run_consumer_manager] Required assignment: {tps}")
                
                if current_assignment != tps:
                    logger.debug(f"[run_consumer_manager] Assignment change needed for {bootstrap_server}")
                    update_bootstrap_servers.append(bootstrap_server)

            # 检查需要删除的消费者
            for bootstrap_server in self.consumers:
                if bootstrap_server not in bootstrap_servers_topics:
                    logger.debug(f"[run_consumer_manager] Server to be removed: {bootstrap_server}")
                    delete_bootstrap_servers.append(bootstrap_server)

            # 日志记录变更情况
            if update_bootstrap_servers:
                logger.info(f"[run_consumer_manager] Servers to update: {'|'.join(update_bootstrap_servers)}")
            if create_bootstrap_servers:
                logger.info(f"[run_consumer_manager] Servers to create: {'|'.join(create_bootstrap_servers)}")
            if delete_bootstrap_servers:
                logger.info(f"[run_consumer_manager] Servers to delete: {'|'.join(delete_bootstrap_servers)}")

            if any([update_bootstrap_servers, create_bootstrap_servers, delete_bootstrap_servers]):
                logger.debug("[run_consumer_manager] Changes detected, acquiring lock")
                self.consumers_lock.acquire()
                logger.debug("[run_consumer_manager] Lock acquired, starting consumer updates")
                new_consumers = {}

                # 创建新消费者
                for bootstrap_server in create_bootstrap_servers:
                    logger.debug(f"[run_consumer_manager] Creating new consumer for {bootstrap_server}")
                    new_consumers[bootstrap_server] = KafkaConsumer(
                        bootstrap_servers=bootstrap_server,
                        group_id=f"{settings.APP_CODE}.alert.builder",
                        max_partition_fetch_bytes=1024 * 1024 * 5,
                    )
                    logger.debug(f"[run_consumer_manager] Initializing new consumer for {bootstrap_server}")
                    new_consumers[bootstrap_server].poll()
                    new_consumers[bootstrap_server].assign(partitions=list(bootstrap_servers_topics[bootstrap_server]))
                    
                    # 设置偏移量
                    for tp in bootstrap_servers_topics[bootstrap_server]:
                        logger.debug(f"[run_consumer_manager] Setting up offset for {tp}")
                        data_id = self.topic_data_id.get(f"{bootstrap_server}|{tp.topic}")
                        logger.debug(f"[run_consumer_manager] Found data_id: {data_id} for topic {tp.topic}")
                        
                        if not data_id or tp.partition != 0:
                            logger.debug(f"[run_consumer_manager] Skipping offset setup for partition {tp.partition}")
                            continue
                            
                        redis_offset = self.get_kafka_redis_offset(data_id=data_id, topic=tp.topic)
                        if redis_offset:
                            logger.debug(f"[run_consumer_manager] Setting offset {redis_offset} for {tp}")
                            new_consumers[bootstrap_server].seek(tp, redis_offset)

                # 处理现有消费者
                for bootstrap_server, consumer in self.consumers.items():
                    if bootstrap_server in delete_bootstrap_servers:
                        logger.debug(f"[run_consumer_manager] Closing consumer for {bootstrap_server}")
                        self.close_consumer(consumer)
                        continue

                    if bootstrap_server in update_bootstrap_servers:
                        logger.debug(f"[run_consumer_manager] Updating assignments for {bootstrap_server}")
                        consumer.assign(partitions=list(bootstrap_servers_topics[bootstrap_server]))
                    new_consumers[bootstrap_server] = consumer

                logger.debug("[run_consumer_manager] Updating consumers dictionary")
                self.consumers = new_consumers
                logger.debug("[run_consumer_manager] Releasing lock")
                self.consumers_lock.release()
            else:
                logger.info("[run_consumer_manager] No consumer changes needed, current count: %s", len(list(self.consumers.values())))

            if self._stop_signal:
                logger.debug("[run_consumer_manager] Stop signal received")
                self.consumers_lock.acquire()
                logger.info("[run_consumer_manager] Closing all consumers")
                map(lambda c: self.close_consumer(c), self.consumers.values())
                self.consumers = {}
                self.consumers_lock.release()
                logger.debug("[run_consumer_manager] All consumers closed")
                break
            
            if self.run_once:
                logger.debug("[run_consumer_manager] Run once mode, breaking loop")
                break
            
            logger.debug("[run_consumer_manager] Sleeping for 15 seconds")
            time.sleep(15)
            logger.debug("[run_consumer_manager] Waking up from sleep")

    @staticmethod
    def close_consumer(consumer):
        logger.debug("[close_consumer] Committing and closing consumer")
        consumer.commit()
        consumer.close()

    @always_retry(10)
    def run_poller(self):
        logger.debug("[run_poller] Starting poller process")
        if self.consumers_lock.locked():
            logger.debug("[run_poller] Releasing locked consumer lock")
            self.consumers_lock.release()
        
        while True:
            logger.debug("[run_poller] Starting new polling cycle")
            logger.debug("[run_poller] Attempting to acquire consumer lock")
            self.consumers_lock.acquire()
            logger.debug("[run_poller] Consumer lock acquired successfully")
            
            has_record = False
            logger.debug(f"[run_poller] Current active consumers: {list(self.consumers.keys())}")
            
            for bootstrap_server, consumer in self.consumers.items():
                logger.debug(f"[run_poller] Polling from bootstrap_server: {bootstrap_server}")
                logger.debug(f"[run_poller] Using max_records: {self.MAX_RETRIEVE_NUMBER}")
                
                data = consumer.poll(500, max_records=self.MAX_RETRIEVE_NUMBER)
                logger.debug(f"[run_poller] Poll result for {bootstrap_server}: {'empty' if not data else 'has data'}")
                
                if not data:
                    logger.debug(f"[run_poller] No data received from {bootstrap_server}, continuing to next consumer")
                    continue

                has_record = True
                events = []
                logger.debug(f"[run_poller] Processing {len(data)} partition records from {bootstrap_server}")

                for records in list(data.values()):
                    events.extend(records)
                
                logger.debug(f"[run_poller] Total events collected: {len(events)}")
                logger.debug(f"[run_poller] Pushing events to handler task for {bootstrap_server}")
                self.push_handle_task(consumer.config['bootstrap_servers'], events)
                
                logger.info(
                    "[run_poller] alert event poller poll %s: count(%s)",
                    consumer.config['bootstrap_servers'],
                    len(events),
                )
            
            logger.debug("[run_poller] Releasing consumer lock")
            self.consumers_lock.release()
            logger.debug("[run_poller] Consumer lock released")

            if self.run_once or self._stop_signal:
                logger.debug("[run_poller] Detected stop condition (run_once: %s, stop_signal: %s)", 
                            self.run_once, self._stop_signal)
                logger.info("[run_poller] alert event poller got stop signal")
                break

            if not has_record and self.consumers:
                logger.debug("[run_poller] No records found in this cycle")
                logger.info(
                    "[run_poller] alert event poller get no data from %s", 
                    ",".join(list(self.consumers.keys()))
                )

            if not self.consumers:
                logger.debug("[run_poller] No active consumers, preparing to sleep")
                time.sleep(5)
                logger.info("[run_poller] sleep(5 seconds) because of no consumer")
                logger.debug("[run_poller] Waking up from sleep")
                continue
            
            logger.debug("[run_poller] Completed polling cycle")

    def get_kafka_redis_offset(self, data_id, topic):
        """
        获取redis记录的offset
        """
        prefix = f"{settings.APP_CODE}_kafka_offset"
        group = f"alert.builder.{data_id}"
        offset_key = "_".join(map(str, [prefix, group, topic]))
        offset = self.redis_client.get(offset_key)
        # 删除掉记录的key
        self.redis_client.delete(offset_key)
        return offset

    def push_handle_task(self, bootstrap_server, events):
        logger.debug(f"[push_handle_task] Processing {len(events)} events from {bootstrap_server}")
        # 分批次推送至告警生成任务
        for event_index in range(0, len(events), self.max_event_number):
            batch_size = min(self.max_event_number, len(events) - event_index)
            logger.debug(f"[push_handle_task] Sending batch of {batch_size} events")
            # 分发处理任务
            self.send_handler_task(
                event_kwargs={
                    "topic_data_id": self.topic_data_id,
                    "bootstrap_server": bootstrap_server,
                    "events": events[event_index : event_index + self.max_event_number],
                }
            )

    def send_handler_task(self, event_kwargs):
        run_alert_builder(**event_kwargs)


class AlertCeleryHandler(AlertHandler):
    def __init__(self, service, *args, **kwargs):
        super(AlertCeleryHandler, self).__init__(service, *args, **kwargs)
        self.run_once = False

    def send_handler_task(self, event_kwargs):
        run_alert_builder.delay(**event_kwargs)