# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云 - 监控平台 (BlueKing - Monitor) available.
Copyright (C) 2017-2021 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""

import logging

import arrow
import kafka

from kafka import KafkaConsumer
from kafka import TopicPartition
import kafka.errors
from django.conf import settings
from six.moves import map, range

from alarm_backends.constants import CONST_ONE_DAY
from alarm_backends.core.storage.redis import Cache
from constants.strategy import MAX_RETRIEVE_NUMBER

logger = logging.getLogger("core.storage.kafka")


class KafkaQueue(object):
    reconnect_seconds = getattr(settings, "KAFKA_RECONNECT_SECONDS", 60)

    def __init__(self, topic="", group_prefix="", redis_offset=True, kfk_conf=None, timeout=6):
        self.redis_offset = redis_offset
        if kfk_conf:
            kafka_hosts = "{}:{}".format(kfk_conf["domain"], kfk_conf["port"])
        else:
            kafka_hosts = f"{settings.KAFKA_HOST[0]}:{settings.KAFKA_PORT}"
        self.kafka_hosts = kafka_hosts
        self._client = kafka.SimpleClient(hosts=kafka_hosts, timeout=timeout)
        self._client_init_time = arrow.utcnow()
        self.producer = kafka.producer.SimpleProducer(self.client)
        self.consumer_pool = {}
        self.offset_manager_pool = {}
        self.set_topic(topic, group_prefix)

    def __del__(self):
        self.close()

    @classmethod
    def get_common_kafka_queue(cls):
        kfk_conf = {"cluster_index": 0, "domain": settings.KAFKA_HOST[0], "port": settings.KAFKA_PORT}
        return cls(kfk_conf=kfk_conf)

    def close(self):
        self.client.close()

    @property
    def client(self):
        now = arrow.utcnow()
        delta = (now - self._client_init_time).total_seconds()
        if delta > self.reconnect_seconds:
            self._client_init_time = now
            self._client.reinit()
        return self._client

    def set_topic(self, topic, group_prefix=""):
        if topic:
            self.topic = topic
        if group_prefix:
            self.group_name = "{}{}".format(group_prefix, settings.KAFKA_CONSUMER_GROUP)

    def get_producer(self):
        if hasattr(self, "producer"):
            return self.producer
        self.producer = kafka.producer.SimpleProducer(self.client)
        return self.producer

    def get_consumer(self):
        consumer_pool_key = "{}-{}".format(self.topic, self.group_name)
        # 增加初始化判断机制 关键值未赋值 则初始化不成功
        consumer = self.consumer_pool.get(consumer_pool_key)
        if not consumer:
            consumer = self._create_consumer(self.topic, self.group_name)
            self.consumer_pool[consumer_pool_key] = consumer
        return consumer

    def get_offset_manager(self):
        offset_manager_pool_key = "{}-{}".format(self.topic, self.group_name)
        # 增加初始化判断机制 关键值未赋值 则初始化不成功
        if self.offset_manager_pool.get(offset_manager_pool_key):
            offset_manager = self.offset_manager_pool.get(offset_manager_pool_key)
        else:
            offset_manager = KafkaOffsetManager(self.get_consumer(), self.topic)
            self.offset_manager_pool[offset_manager_pool_key] = offset_manager
        return offset_manager

    # 重试3次 确认offsets赋值成功
    def _create_consumer(self, topic, group_name):
        for i in range(3):
            try:
                # consumer = kafka.consumer.SimpleConsumer(
                #     self.client, group_name, topic, auto_commit=settings.KAFKA_AUTO_COMMIT, max_buffer_size=None
                # )
                consumer = KafkaConsumer(
                    bootstrap_servers=self.kafka_hosts,
                    group_id=group_name,
                    enable_auto_commit=False,  # 禁用自动提交偏移量
                    # fetch_min_bytes=1,  # 最小拉取消息字节数
                    # fetch_max_wait_ms=500,  # 最大等待时间
                    max_poll_records=MAX_RETRIEVE_NUMBER, # 每次拉取的最大记录数
                    # 和simpleconsumer保持一致
                    auto_offset_reset='largest',
                )
            except Exception as e:
                logger.exception(
                    "topic(%s) create consumer failed %d times: %s",
                    topic,
                    i + 1,
                    e,
                )
                continue
            if consumer.offsets:
                return consumer
            else:
                logger.warning("topic(%s) load metadata failed %d times", topic, i + 1)
                continue
        else:
            logger.error("topic %s load metadata failed", topic)
            raise Exception("topic {} load metadata failed".format(topic))

    def put(self, value, topic=""):
        if not isinstance(value, list):
            value = [value]
        try:
            if topic:
                return self.get_producer().send_messages(topic, *value)
            else:
                return self.get_producer().send_messages(self.topic, *value)
        except kafka.errors.FailedPayloadsError:
            if topic:
                return self.get_producer().send_messages(topic, *value)
            else:
                return self.get_producer().send_messages(self.topic, *value)

    def reset_offset(self, force_offset=-1):
        """
        修改：get_tail函数传入参数topic，使用end_offset获取最大值
        """
        if force_offset >= 0:
            new_offset = force_offset
        else:
            offset = self.get_offset_manager().get_offset()
            tail = self.get_offset_manager().get_tail(self.topic) - 5
            new_offset = max(offset, tail)
        self.get_offset_manager().set_offset(new_offset, True)

    def take_raw(self, count=1, timeout=5):
        """
        从Kafka获取消息
        Args:
            count: 需要获取的消息数量
            timeout: 超时时间(秒)
        Returns:
            messages: 消息列表
        """
        messages = []
        if self.redis_offset:
            self.get_offset_manager().reset_consumer_offset(count, self.topic)

        try:
            # poll返回一个字典 {TopicPartition: [messages]}
            records = self.get_consumer().poll(timeout_ms=timeout*1000, max_records=count)

            # 处理获取到的消息
            for tp, msgs in records.items():
                for msg in msgs:
                    messages.append(msg)

        except kafka.errors.KafkaError as e:
            logger.error("Kafka error while fetching messages: %s", e)
        except Exception as e:
            logger.info("get kafka info error", e)
        finally:
            if settings.KAFKA_AUTO_COMMIT:
                if self.get_consumer().commit() is False:
                    logger.warning("Kafka commit failure")
        if self.redis_offset:
            self.get_offset_manager().update_consumer_offset(count, messages, self.topic)
        return messages

    def take(self, count=1, timeout=0.1):
        return [m.value for m in self.take_raw(count, timeout)]


class KafkaOffsetManager(object):
    TIMEOUT = CONST_ONE_DAY
    KEY_PREFIX = "{}_kafka_offset".format(settings.APP_CODE)

    def __init__(self, consumer, topic):
        # consumer和topic需要在self.get_offset()方法使用，因此需要放到前面
        self.consumer = consumer
        self.topic = topic
        self.cache = Cache("service")
        self.instance_offset = self.get_offset()
        self.reset_offset = 0  # 当前的重置点

    @property
    def key(self):
        return "_".join(map(str, [self.KEY_PREFIX, self.consumer.config["group_id"], self.topic]))

    @property
    def reset_key(self):
        return "RESET_%s" % self.key

    def _get_offset(self):
        return self.cache.get(self.key)

    def get_offset(self):
        return int(self._get_offset() or 0)

    def get_reset_offset(self):
        return int(self.cache.get(self.reset_key) or 0)

    def set_offset(self, offset, force=False):
        if not force and self.get_reset_offset() != self.reset_offset:
            return logger.info("Kafka_offset pass set")
        logger.debug("Kafka_offset local %s: %s", self.key, offset)
        self.cache.set(self.key, offset, self.TIMEOUT)
        return self.get_offset()

    def set_seek(self, topic):
        """
        修改：使用kafkaconsumer设置seek
        """
        partitions = self.get_partitions(topic)
        remote_offset = self.get_end_offset(topic)
        self.consumer.assign(partitions)
        self.consumer.seek(partitions[0], remote_offset)
        self.cache.set(self.key, remote_offset, self.TIMEOUT)
        return self.get_offset()

    def set_reset_offset(self, offset):
        logger.debug("Kafka_offset set_reset %s: %s", self.key, offset)
        self.cache.set(self.reset_key, offset, self.TIMEOUT)
        return self.set_offset(offset, force=True)

    def get_partitions(self, topic):
        """
        新增：获取TopicPartition类型的partitions
        """
        partitions = self.consumer.partitions_for_topic(topic)
        if partitions is None:
            self.consumer.topics()
            partitions = self.consumer.partitions_for_topic(topic)

        partitions = {0} if partitions is None else partitions
        final_partitions = [TopicPartition(topic=topic, partition=tp) for tp in partitions]
        return final_partitions

    def _set_consumer_offset(self, new_remote_offset, topic):
        """
        修改：修改设置consumer_offset方式使用assign和seek
        """
        partitions = self.get_partitions(topic)

        self.consumer.assign(partitions)
        self.consumer.seek(partitions[0], new_remote_offset)

    def get_end_offset(self, topic):
        """
        新增：获取最新offset
        """
        partitions = self.get_partitions(topic)
        end_offsets = self.consumer.end_offsets(partitions)

        offset = []
        for partition in partitions:
            offset.append(end_offsets[partition])

        return max(offset) if len(offset) > 0 else 0

    def reset_consumer_offset(self, count, topic):
        """
        修改：当_get_offset()为空时，从end_offset设置
        """

        reset_offset = self.get_reset_offset()
        # 如果有新的重置点，那么当前游标设置为重置点
        if reset_offset and reset_offset != self.reset_offset:
            self.instance_offset = self.reset_offset = reset_offset
        # 如果第一次读这个 topic，那么当前游标设置为最新前 3 条
        if self._get_offset() is None:
            # 修改：此处拿end_offset
            max_offset = self.get_end_offset(topic)
            self.instance_offset = max_offset - 3 if max_offset >= 3 else 0

        # 否则从 redis 读取游标
        else:
            self.instance_offset = self.get_offset()

        # 更新 client 的游标
        self._set_consumer_offset(self.instance_offset, topic)

        new_local_offset = self.instance_offset + count
        if self.get_offset() < new_local_offset:
            self.instance_offset = self.set_offset(new_local_offset)

    def get_tail(self, topic):
        """
        修改：获取最大offset，可以拿end_offset
        """
        return self.get_end_offset(topic)

    def update_consumer_offset(self, count, messages, topic):
        if not messages:
            partition = self.get_partitions(topic)[0]
            new_offset = self.consumer.committed(partition)
            self.instance_offset = self.set_offset(new_offset)
        elif len(messages) < count:
            offset = messages[-1].offset + 1
            logger.debug("Kafka_offset local_desc %s: %s to %s", self.key, self.instance_offset, offset)
            self.instance_offset = self.set_offset(offset)
