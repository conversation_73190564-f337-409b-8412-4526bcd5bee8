
# 1.2.89
    * 修复升级SQL
# 1.2.88
    * 合并社区版
# 1.2.80
    * 修复清洗安装初始化问题
# 1.2.79
    * 精简安装包
# 1.2.78
    * 优化第一次接入出图
    * 修复自定义组件等功能引入的tsdb connector添加bug
    * 修复清洗配置提取逻辑
# 1.2.77
    * 支持脚本采集
# 1.2.73
    * 监控支持exporter
    * 监控支持进程基础性能
# 1.2.70
    * 监控后台API取消平台ID的兼容逻辑
    * 增加获取总线集群\重启任务接口
# 1.2.69
    * 取消平台ID的兼容逻辑
# 1.2.59
    * windows采集器使用bat部署采集器
# 1.2.55
    * 修复日志检索app创建的索引由app调用删除ES索引接口删除，不做定时删除
    * 修复删除MySQL清理数据crontab定时任务
    * 修复mysql_driver修改字段逻辑
# 1.2.49
    * 添加标准版升级到国际版的sql
# 1.2.48
    * 删除多余包文件
# 1.2.47
    * 删除不允许商用的包
# 1.2.44
    * 修复查询接口使用utc时间
    * 新增databus检查脚本, 方便排查问题
# 1.2.40
    * 修复etl包未引用国际化包的问题
    * 监控后台API修复operator问题
# 1.2.37
    * 修复1.2.36引入的包异常
# 1.2.36
    * 监控API修复header中未传递language的问题
# 1.2.35
    * 防护时间字段没有传递使用的Int转换失败
# 1.2.34
    * 监控自定义支持指定时区
    * 日志检索使用_utctime_作为时间字段
# 1.2.33
    * 更新初始化提示语言
# 1.2.32
    * 更新sql_parse
# 1.2.31
    * 修复init data环节key error
    * 更新ja
# 1.2.30
    * 修复celery任务代码未翻译的问题
    * 修复部分字典接口未翻译的问题
    * 修复databus.errors包未翻译的问题
# 1.2.26
    * 国际化
# 1.2.21
## 功能列表
    * 丰富databus healthz接口
## 功能修复
    * 修复错误信息编码异常
# 1.2.20
## 功能修复
    * 修复更新cc缓存未更新bizid的问题
# 1.2.17
## 功能修复
    * 修复下发超时, 增加异步接口
    * 去掉敏感信息
# 1.2.14
## 功能列表
    * 增加redis清洗配置
## 功能修复
    * 修复下发组件配置覆盖的问题
    * 修复部分组件指标清洗失败的问题
# 1.2.12
## 功能修复
    * 修复下发组件失败
    * 调整字段名
# 1.2.11
## 功能修复
    * 修复日志检索无法查询告警信息
# 1.2.9 
## 功能修复
    * 修改基础告警配置文件参数
# 1.2.4 
## 功能修复
    * 修复account参数缺失
    * 修复多余的字段信息
# 1.2.3 
## 功能修复
    * 更新settings模板, 临时屏蔽dataflow的代码
    * 去掉旧版本基础性能dataid申请逻辑
# 1.2.1 
## 功能列表
    * 调整先influxdb启动逻辑
    * 新组件部署接口
# 1.2.0 
## 功能列表
    * 支持tsdb
# 1.1.10
## 功能修复
    * 删除调试信息
# 1.1.7 
## 功能修复
    * 增加缺失包
    * 修复初始化sql错误
# 1.1.5 
## 功能修复
    * 修复初始化脚本错误
# 1.1.4 
## 功能列表
    * 删除es索引接口
## 功能修复
    * 去掉对admin账户的依赖
    * 优化bkdata初始化错误显示
# 1.1.0 
## 功能列表
    * 快照数据初始化
# 1.0.9
## 功能修复
    * 删除调试配置
# 1.0.6
## 功能修复
    * windows主机不支持基础告警，导致提示主机监控失败（基础性能设置成功）
# 1.0.5
## 功能修复
    * 修复采集停止失败
# 1.0.4
## 功能修复
    * setting的时区设置为系统时区
# 1.0.3
## 功能列表
    * 独立api项目提交流程
# 1.0.2
## 功能列表
    * DataBus API
        - 修复部分监控字段类型与上报长度不匹配的问题

# 1.0.1
## 功能列表
    * DataBus API
        - 下发采集器支持跨云
        - 修复自定义日志清洗配置中的分隔符错误

# 1.0.0 初始版本
## 功能列表
    * 监控API
        - 主机监控
        - 自定义监控
        - 数据源管理
        - 事件中心
    * 查询API
        - 结果表数据查询[ES, MYSQL]
        - 结果表配置管理
    * DataBus API
        - 数据配置管理
        - 数据清洗入库配置管理
        - 采集配置下发

