# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-20 11:35+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "参数错误"
msgstr "Parameter error"

msgid "用户权限不足"
msgstr "Insufficient user rights"

msgid "非法请求"
msgstr "illegal request"

msgid "失败请求,系统错误"
msgstr "failed request, system error"

msgid "请求外部接口错误"
msgstr "Error requesting external interface"

msgid "TRT错误"
msgstr "TRT error"

msgid "参数校验失败:{wrap}"
msgstr "Parameter validation failed: {wrap}"

msgid "{wrap}{prefix}第{index}项:"
msgstr "{wrap}{prefix} item {index}:"

msgid "参数校验失败，详情请查看返回的errors"
msgstr ""
"Parameter verification failed, please refer to the returned errors for "
"details"

msgid "参数校验错误"
msgstr "Parameter validation error"

msgid "调用API异常"
msgstr "API call exception"

msgid "API返回异常"
msgstr "API returns exception"

msgid "对象不存在：{table}[{column}={value}]"
msgstr "Object does not exist: {table}[{column}={value}]"

msgid "对象已存在，无法添加：{table}[{column}={value}]"
msgstr "Object already exists and cannot be added: {table}[{column}={value}]"

msgid "该模型已注册为需要进行数据同步的ORM模型"
msgstr ""
"The model is registered as an ORM model that requires data synchronization"

msgid "元数据信息同步失败"
msgstr "Synchronization of metadata information failed"

msgid "元数据同步类注册失败"
msgstr "Metadata synchronization class registration failed"

msgid "正常响应"
msgstr "normal response"

msgid "API不存在"
msgstr "API does not exist"

msgid "系统异常"
msgstr "System exception"

msgid "普通异常"
msgstr "common exception"

msgid "带有参数的异常，{param1}，{param2}"
msgstr "Exception with parameters, {param1}, {param2}"

msgid "实体类型({type})不支持标签"
msgstr "Entity type ({type}) does not support tags"

msgid "目标实体({type}:{id})不存在"
msgstr "The target entity ({type}:{id}) does not exist"

msgid "标签({code})不存在"
msgstr "Tag ({code}) does not exist"

msgid "操作元数据系统RPC接口失败。错误：{inner_error}"
msgstr ""
"Failed to operate the metadata system RPC interface. Error: {inner_error}"

msgid "请求的数据不合规。错误：{inner_error}"
msgstr "The requested data is not compliant. Error: {inner_error}"

msgid "元数据支持流程错误。详细信息：{inner_error}"
msgstr "Metadata support process error. Details: {inner_error}"

msgid "该装饰器只允许用于Django的View函数(包括普通View函数和Class-base的View函数)"
msgstr ""
"This decorator is only allowed for Django's View functions (including "
"ordinary View functions and Class-based View functions)"

msgid "API请求异常，请管理员排查问题"
msgstr ""
"The API request is abnormal, please ask the administrator to troubleshoot "
"the problem"

msgid "API请求异常，状态码（{code}），请管理员排查问题"
msgstr ""
"The API request is abnormal, the status code ({code}), please ask the "
"administrator to troubleshoot the problem"

msgid "返回数据格式不正确，结果格式非json."
msgstr ""
"The format of the returned data is incorrect, and the result format is not "
"json."

msgid "异常请求方式，{method}"
msgstr "Abnormal request method, {method}"

msgid "请求参数中请包含参数{key}"
msgstr "Please include the parameter {key} in the request parameter"

msgid "请求方法{key}不存在"
msgstr "Request method {key} does not exist"

msgid "获取用户信息"
msgstr "Get user information"

msgid "查询app信息"
msgstr "Query app information"

msgid "发起查询任务"
msgstr "Initiate a query task"

msgid "操作进程"
msgstr "operation process"

msgid "查询任务结果"
msgstr "Query task results"

msgid "查询Agent状态"
msgstr "Query Agent status"

msgid "快速分发文件"
msgstr "Distribute files quickly"

msgid "GSE 文件内容推送"
msgstr "GSE file content push"

msgid "查询ip任务结果日志"
msgstr "Query the ip task result log"

msgid "查询进程结果"
msgstr "query process results"

msgid "快速执行脚本"
msgstr "Execute scripts quickly"

msgid "获取文件"
msgstr "get file"

msgid "查询GSE获取文件结果"
msgstr "Query GSE to get file results"

msgid "GSE进程托管注册和取消注册"
msgstr "GSE process hosting registration and deregistration"

msgid "检查用户是否具有指定权限"
msgstr "Check if the user has the specified permissions"

msgid "批量检测是否有指定权限"
msgstr "Batch check whether there are specified permissions"

msgid "获取用户有权限的对象范围"
msgstr "Get the range of objects to which the user has permissions"

msgid "获取用户有权限的维度"
msgstr "Get the dimensions for which the user has permissions"

msgid "检查 Token 授权码的权限"
msgstr "Check the permission of the token authorization code"

msgid "获取 TOKEN 信息"
msgstr "Get TOKEN information"

msgid "更新项目的用户列表"
msgstr "Update the user list for a project"

msgid "检查项目的数据权限"
msgstr "Check the data permissions of the project"

msgid "同步权限"
msgstr "sync permissions"

msgid "检查项目是否有集群组权限"
msgstr "Check if the project has cluster group permissions"

msgid "置换默认的 DATA_TOKEN"
msgstr "Replace the default DATA_TOKEN"

msgid "获取网关公开密钥"
msgstr "Get gateway public key"

msgid "创建DB操作记录"
msgstr "Create DB operation record"

msgid "查询血缘关系"
msgstr "Query blood relationship"

msgid "语言对照配置表"
msgstr "Language comparison configuration table"

msgid "原生后端查询语言搜索"
msgstr "Native Backend Query Language Search"

msgid "业务接口"
msgstr "business interface"

msgid "请求元数据接口异常({})，数据同步失败"
msgstr ""
"Request metadata interface exception ({}), data synchronization failed"

msgid "请求元数据接口失败({})，数据同步失败"
msgstr ""
"Failed to request metadata interface ({}), data synchronization failed"

msgid "{} | {} | 调度ID：{}"
msgstr "{} | {} | Schedule ID: {}"

msgid "错误已记录. | 调度ID：{}"
msgstr "Error logged. | Dispatch ID: {}"

msgid "RPC流程失败:{}."
msgstr "RPC process failed: {}."

msgid "RPC返回结果为空."
msgstr "The RPC return result is empty."

msgid "字段级"
msgstr "field level"

msgid "表级"
msgstr "table level"

msgid "业务级"
msgstr "business level"

msgid "bk_username不可为空"
msgstr "bk_username cannot be empty"

msgid "bk_app_code不可为空"
msgstr "bk_app_code cannot be empty"

msgid "data_token不可为空"
msgstr "data_token cannot be empty"

msgid "权限基类"
msgstr "permission base class"

msgid "权限不足"
msgstr "Insufficient permissions"

msgid "必要参数缺失"
msgstr "Required parameter missing"

msgid "认证不通过，请提供合法的 BKData 认证信息"
msgstr ""
"Authentication failed, please provide legal BKData authentication "
"information"

msgid "内部模块调用请传递准确的 bk_app_code 和 bk_app_secret"
msgstr ""
"Please pass accurate bk_app_code and bk_app_secret for internal module calls"

msgid "请传递合法的 data_token"
msgstr "Please pass a legal data_token"

msgid "非白名单 APP 不可直接访问接口"
msgstr "Non-whitelist APPs cannot directly access the interface"

msgid "未检测到有效的认证信息"
msgstr "No valid authentication information detected"

msgid "错误的认证方式"
msgstr "wrong authentication method"

msgid "ESB 传递的 JWT 字符串解析失败"
msgstr "Parsing of JWT string passed by ESB failed"

msgid "参数 bk_username 不可为空"
msgstr "The parameter bk_username cannot be empty"

msgid "聚合函数"
msgstr "aggregate function"

msgid "求和"
msgstr "to sum"

msgid "平均"
msgstr "average"

msgid "统计数值"
msgstr "Statistics"

msgid "详情"
msgstr "details"

msgid "实例ID"
msgstr "Instance ID"

msgid "业务"
msgstr "business"

msgid "项目"
msgstr "project"

msgid "名称"
msgstr "name"

msgid "配置"
msgstr "configuration"

msgid "你恐怕用的是假的数据平台"
msgstr "You're probably using a fake data platform"

msgid "获取结果表元信息"
msgstr "Get result table meta information"

msgid "二级资源DEMO"
msgstr "Secondary resource DEMO"

msgid "您所请求的内容不存在"
msgstr "The content you requested does not exist"

msgid "请求异常，请联系相关人员"
msgstr "The request is abnormal, please contact the relevant personnel"

msgid "中文"
msgstr "Chinese"
