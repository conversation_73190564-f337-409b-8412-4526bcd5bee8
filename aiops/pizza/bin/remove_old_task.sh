# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """


workdir=$( cd $(dirname $0) && pwd )
conffile=$workdir/../conf/dataapi_settings.py

function get_prop {
    cat $conffile | grep $1 | sed -e 's/ //g; s/"//g' | awk -F '=' '{print $2}'
}


function delete_connector {
    HOST=$(get_prop $1)
    PORT=$(get_prop $2)

    curl --connect-timeout 10 -m 2 http://$HOST:$PORT/connectors \
        | python -m json.tool | grep '\[' -v | grep -v ']' \
        | grep -E 'ja_gse_cpu|ja_gse_cpu_core_max_cpuusage|ja_gse_cpu_cpuusage|ja_gse_cpu_core_cpuusage|ja_gse_disk|ja_gse_disk_iostats|ja_gse_disk_used|ja_gse_net|ja_gse_net_detail|ja_gse_mem' \
        | sed -e 's/"//g;s/,//;s/\s//g' \
        | while read LINE; do echo $LINE; curl -X DELETE http://$HOST:$PORT/connectors/$LINE; echo ""; done
}

for NAME in MYSQL ETL
do
    echo "===========${NAME}==============="
    delete_connector CONNECTOR_${NAME}_HOST CONNECTOR_${NAME}_PORT
    echo "=========================="
    echo
    echo
done

