#!/bin/bash -l
# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """


# 引入蓝鲸环境变量
workon dataapi

NOW=$(date +%Y%m%d%H%M%S)

# base
export PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin:/root/bin:$PATH
# ==================================== helper functions begin ====================================
function echo_step() {
    echo -e '\n\033[0;32m'$1'\033[0m'
}

function info() {
    NOW=$(date +"%Y-%m-%d %H:%M:%S")
    echo "${NOW}"" [INFO] ""$1"
}

function error() {
    NOW=$(date +"%Y-%m-%d %H:%M:%S")
    echo "${NOW}"" [ERROR] ""$1"
}

# -----------------------------------

function if_fail_then_exit() {
    if [ "$1" != "0" ]
    then
        error "$2"
        exit 1
    fi
}

function is_mysql_client_exists() {
    which mysql > /dev/null 2>&1
    if_fail_then_exit "$?" "Mysql Client is required"
}

function test_monitor_db_connection() {
    mysql -h "${MONITOR_DB_HOST}" -P "${MONITOR_DB_PORT}" -u "${MONITOR_DB_USER}" --password="${MONITOR_DB_PASSWORD}" -e "select 1" > /dev/null
    if_fail_then_exit "$?" "Connect to Monitor DB failed! Please check your $BK_HOME/bin/bksuite-bkdata.env"
}

function test_config_db_connection() {
    mysql -h "${CONFIG_DB_HOST}" -P "${CONFIG_DB_PORT}" -u "${CONFIG_DB_USER}" --password="${CONFIG_DB_PASSWORD}" -e "select 1" > /dev/null
    if_fail_then_exit "$?" "Connect to Config db failed! Please check your $BK_HOME/bin/bksuite-bkdata.env"
}

function test_trt_db_connection() {
    mysql -h "${TRT_DB_HOST}" -P "${TRT_DB_PORT}" -u "${TRT_DB_USER}" --password="${TRT_DB_PASSWORD}" -e "select 1" > /dev/null
    if_fail_then_exit "$?" "Connect to Config db failed! Please check your $BK_HOME/bin/bksuite-bkdata.env"
}

function _execute_create_db_sql() {
mysql -h "${DB_HOST}" -P "${DB_PORT}" -u "${DB_USERNAME}" --password="${DB_PASSWORD}" << eof
$1
eof
return $?
}

function create_mapleleaf_db() {
    SQL="CREATE DATABASE IF NOT EXISTS ${DB_NAME} DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;"
    _execute_create_db_sql "${SQL}"
    if_fail_then_exit "$?" "create database ${DB_NAME} failed!"
}

function workon_env() {
    workon "$1"
    if_fail_then_exit "$?" "switch virtualenv to $1 failed"
}

# ==================================== helper functions end ====================================




# ==================================== check and initialize env begin ====================================
# 初始化路径信息
WORK_DIR=$MODULE_HOME/dataapi

# 检查操作系统
OS_INFO=`uname -a`
CENTOS_INFO=`cat /etc/os-release 2>/dev/null | grep PRETTY_NAME`
if [ -z "${CENTOS_INFO}" ]
then
    CENTOS_INFO=`cat /etc/issue 2>/dev/null | head -1`
fi
echo_step "系统信息: [${CENTOS_INFO}] - [${OS_INFO}]"
echo_step "建议系统: centos 6.x/X86_64"

# 检查工作目录
BASEDIR=$(dirname "$0")
cd "${BASEDIR}" || exit

BK_DATA_HOME=$MODULE_HOME
if [ ! "${BK_DATA_HOME}" ]
then
    error "Please set MODULE_HOME (bkdata) first!!"
    exit 1
fi


# 检查配置文件
DATA_API_CONF="$BK_DATA_HOME/dataapi/conf/dataapi.conf"
if [ ! -f $DATA_API_CONF ]; then
    error "Please set $DATA_API_CONF first"
    exit 1
fi
source $DATA_API_CONF

# 执行安装过程
echo_step "1. 进行数据库连接测试"
is_mysql_client_exists
test_monitor_db_connection
test_config_db_connection
test_trt_db_connection

# echo_step "2. 执行mapleleaf database migration"
# create_mapleleaf_db

echo_step "2. 切换到dataapi virtualenv"
workon dataapi

echo_step "3. Install pypi packages"
BIN_DIR=$BK_DATA_HOME/dataapi/bin
yum -y install python-devel
yum -y install mysql-devel
pip install --no-index --find-links=${MODULE_HOME}/support-files/pkgs -r ${WORK_DIR}/requirements.txt
if_fail_then_exit "$?" "pip install failed!"


if [ ! -d "${WORK_DIR}/var" ]; then
    mkdir "${WORK_DIR}"/var
fi
if [ ! -d "${WORK_DIR}/var/log" ]; then
    mkdir "${WORK_DIR}"/var/log
fi
if [ ! -d "${WORK_DIR}/var/run" ]; then
    mkdir "${WORK_DIR}"/var/run
fi

# ==================================== bk_data_api begin ====================================
echo_step "4. 开始安装pizza"

echo_step "5. 拷贝配置"
cp "${WORK_DIR}"/pizza/settings.py.example "${WORK_DIR}"/pizza/settings.py

echo_step "6. 执行数据库变更"
python manage.py migrate trt --database=trt
if_fail_then_exit "$?" "migrate failed!"

