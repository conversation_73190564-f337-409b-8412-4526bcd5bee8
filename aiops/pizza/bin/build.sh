#!/bin/bash
# Ten<PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
#
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
#
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
#
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

# 模块、运行平台、构建号
SUBMODULE=$1
RUN_VERSION=$2
BUILD_NO=$3

echo "\033[92mStart to pack tar file in dist directory ...\033[0m"

# UPIZZA 工作目录、BKBASE 根目录
WORKSPACE=$(pwd)
BKBASE_HOME=$(dirname $(dirname $(dirname "${WORKSPACE}")))

# 总体版本号
PROJECT_VERSION=$(cat ${BKBASE_HOME}/VERSION)
VERSION="${PROJECT_VERSION}-${BUILD_NO}"

DIST_DIR="${WORKSPACE}/dist"
PACKAGE_HOME="${DIST_DIR}/bkdata"
PACKAGE_NAME="${SUBMODULE}api_${RUN_VERSION}_v${VERSION}.tgz"

echo "WORKSPACE=$WORKSPACE"
echo "BKBASE_HOME=$BKBASE_HOME"
echo "DIST_DIR=$DIST_DIR"
echo "PACKAGE_HOME=$PACKAGE_HOME"
echo "PACKAGE_NAME=$PACKAGE_NAME"


# 清理和初始化 dist 目录
rm -rf ${DIST_DIR}
mkdir -p ${DIST_DIR}

# 初始化打包目录
mkdir -p $PACKAGE_HOME
mkdir -p $PACKAGE_HOME/${SUBMODULE}api
mkdir -p $PACKAGE_HOME/support-files
echo "\033[92mSucceed to clear and initail < dist/bkdata > directory\033[0m"

# 移动源码至对应目录
rsync -r --exclude=dist --exclude=.git ${WORKSPACE}/ $PACKAGE_HOME/${SUBMODULE}api
rsync -r ${BKBASE_HOME}/src/api/${SUBMODULE}/ $PACKAGE_HOME/${SUBMODULE}api/${SUBMODULE}
echo "\033[92mSucceed to move pizza and module code\033[0m"

# 生成版本文件
PIZZA_ROOT=$PACKAGE_HOME/${SUBMODULE}api
echo ${VERSION} > ${PIZZA_ROOT}/VERSION
echo "\033[92mSucceed to renew VERSION file\033[0m"

# 合并框架与子模块的 requirements.txt
mv ${PIZZA_ROOT}/requirements.txt ${PIZZA_ROOT}/merged_requirements.txt
if [ -f ${PIZZA_ROOT}/${SUBMODULE}/requirements.txt ]; then
  cat ${PIZZA_ROOT}/${SUBMODULE}/requirements.txt >> ${PIZZA_ROOT}/merged_requirements.txt
fi
if [ -f ${PIZZA_ROOT}/${SUBMODULE}/extend/requirements.txt ]; then
  cat ${PIZZA_ROOT}/${SUBMODULE}/extend/requirements.txt >> ${PIZZA_ROOT}/merged_requirements.txt
fi
cat ${PIZZA_ROOT}/merged_requirements.txt | grep -v "#" | sort | uniq > ${PIZZA_ROOT}/requirements.txt

rm -rf ${PIZZA_ROOT}/*.in
rm -rf ${PIZZA_ROOT}/merged_requirements.txt
rm -rf ${PIZZA_ROOT}/dev_requirements.txt

echo "\033[92mSucceed to merge pizza and module requirements files\033[0m"

# 移动或者生成 project.yml 文件
if [ -f "${PIZZA_ROOT}/${SUBMODULE}/project.yml" ]; then
    rm -f ${PIZZA_ROOT}/project.yml
    cp ${PIZZA_ROOT}/${SUBMODULE}/project.yml ${PIZZA_ROOT}/
    echo "\033[92mSucceed to move submodule project.yml file\033[0m"
else
    sed -i 's/__SUBMODULE_API__/'${SUBMODULE}'api/g' "${PIZZA_ROOT}/project.yml"
    sed -i 's/__RUN_VERSION__/'${RUN_VERSION}'/g' "${PIZZA_ROOT}/project.yml"
    echo "\033[92mSucceed to renew project.yml file\033[0m"
fi

# 移动子模块 on_migrate 文件
if [ -f "${PIZZA_ROOT}/${SUBMODULE}/on_migrate" ]; then
    cp ${PIZZA_ROOT}/${SUBMODULE}/on_migrate ${PIZZA_ROOT}/
    chmod a+x ${PIZZA_ROOT}/on_migrate
    echo "\033[92mSucceed to move submodule on_migrate file\033[0m"
fi

# 移动子模块 pre_start 文件
if [ -f "${PIZZA_ROOT}/${SUBMODULE}/pre_start" ]; then
    cp ${PIZZA_ROOT}/${SUBMODULE}/pre_start ${PIZZA_ROOT}/
    chmod a+x ${PIZZA_ROOT}/pre_start
    echo "\033[92mSucceed to move submodule pre_start file\033[0m"
fi

# 移动子模块 post_start 文件
if [ -f "${PIZZA_ROOT}/${SUBMODULE}/post_start" ]; then
    cp ${PIZZA_ROOT}/${SUBMODULE}/post_start ${PIZZA_ROOT}/
    chmod a+x ${PIZZA_ROOT}/post_start
    echo "\033[92mSucceed to move submodule post_start file\033[0m"
fi

# 移动子模块 release.md 文件
if [ -f "${PIZZA_ROOT}/${SUBMODULE}/release.md" ]; then
    cp ${PIZZA_ROOT}/${SUBMODULE}/release.md ${PIZZA_ROOT}/
    echo "\033[92mSucceed to move submodule release.md file\033[0m"
fi

# 移动子模块 dataapi_settings.py
if [ -f "${PIZZA_ROOT}/${SUBMODULE}/dataapi_settings.py" ]; then
    mv ${PIZZA_ROOT}/${SUBMODULE}/dataapi_settings.py ${PIZZA_ROOT}/conf/
    echo "\033[92mSucceed to move submodule dataapi_settings.py file\033[0m"
fi
if [ -f "${PIZZA_ROOT}/${SUBMODULE}/dataapi_settings_pro.py" ]; then
    mv ${PIZZA_ROOT}/${SUBMODULE}/dataapi_settings_pro.py ${PIZZA_ROOT}/conf/
    echo "\033[92mSucceed to move submodule dataapi_settings_pro.py file\033[0m"
fi

# 移动对应模块的配置文件至打包目录
mkdir ${PACKAGE_HOME}/support-files/metadata
mkdir ${PACKAGE_HOME}/support-files/sql
mkdir ${PACKAGE_HOME}/support-files/templates
mkdir ${PACKAGE_HOME}/support-files/pkgs

# 移动模块 ms/sql/template/pkgs
ls ${BKBASE_HOME}/support-files/api/metadata | grep ${SUBMODULE}api | xargs -I {} cp ${BKBASE_HOME}/support-files/api/metadata/{} ${PACKAGE_HOME}/support-files/metadata/
ls ${BKBASE_HOME}/support-files/api/sql | grep ${SUBMODULE}api |xargs -I {} cp ${BKBASE_HOME}/support-files/api/sql/{} ${PACKAGE_HOME}/support-files/sql/
ls ${BKBASE_HOME}/support-files/api/templates | grep ${SUBMODULE}api | xargs -I {} cp ${BKBASE_HOME}/support-files/api/templates/{} ${PACKAGE_HOME}/support-files/templates/
if [ -d ${BKBASE_HOME}/support-files/api/pkgs ]; then
  cp -rf ${BKBASE_HOME}/support-files/api/pkgs ${PACKAGE_HOME}/support-files/pkgs
fi
echo "\033[92mSucceed to move module ms/sql/templates/pkgs files\033[0m"

# 移动和生成 pizza 依赖的环境文件
cp ${BKBASE_HOME}/support-files/api/templates/upizza#env ${PACKAGE_HOME}/support-files/templates/${SUBMODULE}api#env
echo "APP_NAME=${SUBMODULE}" >> ${PACKAGE_HOME}/support-files/templates/${SUBMODULE}api#env
echo "\033[92mSucceed to move pizza env and add APP_NAME evn variable\033[0m"

# 打包，输出 tar
cd ${DIST_DIR}
tar -czf ${PACKAGE_NAME} bkdata/
rm -rf ./bkdata
ls | grep tgz
echo "\033[92mSucceed to pack and renew tar file(${PACKAGE_NAME})\033[0m"

