# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

import functools
import json

from bk_tinyrpc import RPCCall, RPCClient
from bk_tinyrpc.protocols.jsonrpc import JSONRPCErrorResponse, JSONRPCProtocol
from bk_tinyrpc.transports.http import HttpPostClientTransport
from django.conf import settings as active_settings
from django.utils.translation import ugettext_lazy as _

from ..bklanguage import BkLanguage
from ..exceptions import MetaDataComplianceError, MetaDataRPCCallError
from ..log import logger_api
from ..views import APIView, APIViewSet
from . import resource
from .models import TagTarget
from .private import private_create_tag_to_target, private_delete_tag_to_target
from .resource import keep_ha_access, meta_access_session, metadata_status_dct


def find_related_tag(target_type, target_id):
    return TagTarget.objects.filter(target_type=target_type, target_id=target_id)


def create_tag_to_target(targets, tags):
    return private_create_tag_to_target(targets, tags)


def delete_tag_to_target(targets, tags):
    return private_delete_tag_to_target(targets, tags)


def rpc_communicate(rpc_call_error=MetaDataComplianceError, rpc_error=MetaDataRPCCallError):
    def __func(func):
        @functools.wraps(func)
        def _func(*args, **kwargs):
            try:
                rpc_extra = kwargs.get("rpc_extra", {})
                rpc_extra["language"] = BkLanguage.current_language()
                kwargs["rpc_extra"] = rpc_extra
                data = func(*args, **kwargs)
                if data and isinstance(data[0], tuple):
                    raise data[0][0](data[0][1]).with_traceback(data[0][2])
                if data and isinstance(data[0], JSONRPCErrorResponse):
                    dispatch_id = getattr(data[0], "data", {}).get("dispatch_id", "")
                    if data[0]._jsonrpc_error_code > 0:
                        inner_error = _("{} | {} | 调度ID：{}").format(
                            data[0].error, data[0]._jsonrpc_error_code, dispatch_id
                        )
                    else:
                        inner_error = _("错误已记录. | 调度ID：{}").format(dispatch_id)
                    errors = {
                        "exc": data[0].error
                        if not getattr(data[0], "data", {}).get("exc_msg", None)
                        else data[0].data["exc_msg"]
                    }
                    tb = getattr(data[0], "data", {}).get("tb", None)
                    if tb:
                        errors["tb"] = tb
                    raise rpc_call_error(message_kv={"inner_error": inner_error}, errors=errors)
            except Exception as e:
                if isinstance(e, rpc_call_error):
                    raise
                logger_api.exception("Fail in RPC.")
                raise rpc_error(message_kv={"inner_error": _("RPC流程失败:{}.").format(repr(e))}, errors=repr(e))
            if data:
                return data[0]
            else:
                raise rpc_error(message_kv={"inner_error": _("RPC返回结果为空.")})

        return _func

    return __func


class RPCMixIn:
    @property
    def rpc_client(self):
        if active_settings.METADATA_HA:
            if resource.ha_access == 1:
                keep_ha_access()
            leader = metadata_status_dct.get("leader", None)
            if leader:
                data = json.loads(leader.data)
                endpoint = active_settings.META_ACCESS_RPC_ENDPOINT_TEMPLATE.format(
                    ATLAS_RPC_HOST=data["rpc_host"], ATLAS_RPC_PORT=data["rpc_port"]
                )
                return RPCClient(
                    JSONRPCProtocol(),
                    HttpPostClientTransport(
                        endpoint, meta_access_session.post, timeout=active_settings.META_ACCESS_RPC_TIMEOUT
                    ),
                )
            else:
                return RPCClient(
                    JSONRPCProtocol(),
                    HttpPostClientTransport(
                        active_settings.META_ACCESS_RPC_ENDPOINT,
                        meta_access_session.post,
                        timeout=active_settings.META_ACCESS_RPC_TIMEOUT,
                    ),
                )
        else:
            return RPCClient(
                JSONRPCProtocol(),
                HttpPostClientTransport(
                    active_settings.META_ACCESS_RPC_ENDPOINT,
                    meta_access_session.post,
                    timeout=active_settings.META_ACCESS_RPC_TIMEOUT,
                ),
            )

    @rpc_communicate()
    def entity_complex_search(self, statement, backend_type="mysql", **kwargs):
        kwargs["statement"] = statement
        kwargs["backend_type"] = backend_type
        return self.rpc_client.call_all([RPCCall("entity_complex_search", args=[], kwargs=kwargs)])

    @rpc_communicate()
    def entity_query_via_erp(self, retrieve_args, backend_type="dgraph", **kwargs):
        kwargs["retrieve_args"] = retrieve_args
        kwargs["backend_type"] = backend_type
        return self.rpc_client.call_all([RPCCall("entity_query_via_erp", args=[], kwargs=kwargs)])


class RPCView(RPCMixIn, APIView):
    pass


class RPCViewSet(RPCMixIn, APIViewSet):
    pass
