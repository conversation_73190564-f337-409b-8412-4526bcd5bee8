# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

from collections import defaultdict
from threading import RLock

import requests
from gevent.monkey import is_module_patched
from kazoo.client import KazooClient
from kazoo.handlers.gevent import SequentialGeventHandler
from requests.adapters import HTTPAdapter

from pizza import settings

from ..ha import track_nodes_status

resource_lock = RLock()

adapter = HTTPAdapter(pool_connections=100, pool_maxsize=500)
meta_access_session = requests.session()
meta_access_session.mount("https://", adapter)
meta_access_session.mount("http://", adapter)
if is_module_patched("threading"):
    handler = SequentialGeventHandler()
else:
    handler = None

ha_access = 0


def keep_ha_access():
    with resource_lock:
        global ha_access
        if ha_access != 1:
            return
        global zk
        zk = KazooClient(hosts=settings.ZK_ADDR, handler=handler)
        global metadata_zk_cache
        metadata_zk_cache = track_nodes_status(
            zk, settings.ZK_PATH, metadata_status_dct, cnt=int(settings.METADATA_HA_WAIT_TIME / 0.1)
        )
        ha_access = 2


metadata_status_dct = defaultdict(lambda: None)
if not settings.METADATA_HA:
    ha_access = 0
else:
    ha_access = 1
