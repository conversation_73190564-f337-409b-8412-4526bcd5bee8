# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

from django.conf import settings
from rest_framework.test import APITestCase

from common.auth import perm_check
from common.auth.perms import ProjectPerm, UserPerm
from common.views import APIViewSet


class TestUserPerm(APITestCase):
    def test_check(self):
        self.assertTrue(UserPerm("test_user").check("result_table.retrieve", "591_xxx"))

    def test_list_scope(self):
        UserPerm("test_user").list_scopes("result_table.retrieve")


class TestProjectPerm(APITestCase):
    def test_update_role_users(self):
        self.assertTrue(ProjectPerm(1).update_role_users([{"role_id": "project.manager", "user_ids": ["test_user"]}]))

    def test_check_data(self):
        self.assertFalse(ProjectPerm(1).check_data("591_xxx"))

    def test_check_cluster_group(self):
        self.assertTrue(ProjectPerm(1).check_cluster_group("default"))
        self.assertFalse(ProjectPerm(1).check_cluster_group("default1"))


class TViewSet(APIViewSet):
    lookup_field = "xx_id"

    @perm_check("project.create", detail=False)
    def view1(self, request, xx_id):
        return True

    @perm_check("project.retrieve")
    def view2(self, request, xx_id):
        return True


# class TestTools(APITestCase):

#     def test_perm_check(self):
#         self.assertTrue(TViewSet().view1(None, None, 1))
#         self.assertTrue(TViewSet().view2(None, None, 1))


# class TestIdentity(APITestCase):

#     def test_inner_identity(self):
#         params = {
#             'bk_app_code': settings.APP_NAME,
#             'bk_app_secret': settings.
#         }

#         self.client.get('/v3/demo/auth/test_inner_identity', params)
