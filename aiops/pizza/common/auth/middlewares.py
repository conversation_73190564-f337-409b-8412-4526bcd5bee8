# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

import json

from django.http import JsonResponse
from django.middleware.common import MiddlewareMixin

from common.local import get_local_param, set_local_param
from conf import dataapi_settings

from .exceptions import BaseAuthError
from .identities import InnerIdentity, TokenIdentity, UserIdentity, exchange_default_token_identity, extract_value


def _convert_exception_to_response(exc):
    """
    将权限模块的异常输出转换为标准响应

    @param {BaseAuthError} exc
    """
    return JsonResponse({"result": False, "message": exc.message, "data": None, "code": exc.code, "errors": None})


class AuthenticationMiddleware(MiddlewareMixin):
    # 身份认证器，次序决定认证次序
    IdentityBackends = [InnerIdentity, TokenIdentity, UserIdentity]

    @classmethod
    def match_backend(cls, bkdata_authentication_method):
        for backend in cls.IdentityBackends:
            if backend.NAME == bkdata_authentication_method:
                return backend

        return None

    def process_request(self, request):
        bkdata_authentication_method = extract_value(request, "bkdata_authentication_method")
        identity = None

        try:
            backend = self.match_backend(bkdata_authentication_method)
            if backend:
                ret = backend.authenticate(request)
                if ret is not None:
                    identity = ret

            # 是否支持默认 TOKEN，调用方可以选择不传入 TOKEN
            if identity is None and getattr(dataapi_settings, "SUPPORT_DEFAULT_TOKEN", False):
                identity = exchange_default_token_identity(request)

            if identity is None:
                # todo，现阶段对于没有认证信息，非法途径过来的调用，仍旧通过，后续会进行限制
                # logger.info('[No Identity] xxx')
                # raise AuthenticateError()
                pass
        except BaseAuthError as e:
            return _convert_exception_to_response(e)

        set_local_param("identity", identity)

        if identity is not None:
            bk_username = identity.bk_username
            bk_app_code = identity.bk_app_code
        else:
            # 保留老逻辑获取用户名和 APP_CODE 的方式，避免业务代码逻辑获取不到用户名和 APP_CODE
            bk_username = extract_value(request, "bk_username")
            bk_app_code = extract_value(request, "bk_app_code")

        # 为了兼容框架中从 local 文件中获取当前访问的 APP_CODE 和 USERNAME
        set_local_param("bk_username", bk_username)
        set_local_param("bk_app_code", bk_app_code)

        # 认证信息，用于直接透传给esb
        try:
            auth_info = json.loads(extract_value(request, "auth_info"))
        except (ValueError, TypeError):
            auth_info = {}

        set_local_param("auth_info", auth_info)

        return None


def get_identity():
    """
    获取当前访问的身份
    """
    return get_local_param("identity")


def get_request_username():
    """
    获取当前访问身份的用户名
    """
    return get_identity().bk_username


def get_request_app_code():
    """
    获取当前访问身份的APP
    """
    return get_identity().bk_app_code
