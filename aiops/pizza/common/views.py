# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

import json

from rest_framework import serializers
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet, ModelViewSet

try:
    from opentelemetry.trace import get_current_span
except ImportError:
    get_current_span = None

from dispatching.runtime import context

from .mixins import LoggingMixin, ResponseMixin, ValidationMixin


class APIView(ResponseMixin, LoggingMixin, ValidationMixin, APIView):
    def check_throttles(self, request):
        """
        Check if request should be throttled.
        Raises an appropriate exception if the request is throttled.
        """
        for throttle in self.get_throttles():
            if not throttle.allow_request(request, self):
                self.throttled(request, throttle.wait())


class APIViewSet(ResponseMixin, LoggingMixin, ValidationMixin, GenericViewSet):
    serializer_class = serializers.Serializer

    def initial(self, request, *args, **kwargs):
        module_info = tuple(self.__module__.split("."))
        if len(module_info) >= 3 and module_info[:2] == (
            "aiops",
            "logic",
        ):
            context.local.stage = module_info[2]
        return super().initial(request, *args, **kwargs)

    def finalize_response(self, request, response, *args, **kwargs):
        if get_current_span:
            span = get_current_span()
            attrs = {
                "aiops.stage": getattr(context.local, "stage", None),
                "aiops.stage_id": getattr(context.local, "stage_id", None),
            }
            for k, v in attrs.items():
                v = json.dumps(v) if not isinstance(v, (int, float, str, bytes)) else v
                attrs[k] = v
            span.set_attributes(attrs)
        return super().finalize_response(request, response, *args, **kwargs)


class APIModelViewSet(ResponseMixin, LoggingMixin, ValidationMixin, ModelViewSet):
    pass
