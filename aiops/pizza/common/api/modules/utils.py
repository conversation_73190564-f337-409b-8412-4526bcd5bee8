# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

import sys

from common import local
from common.local import get_request_username
from conf.dataapi_settings import APP_ID, APP_TOKEN


def is_celery():
    """
    判断当前环境是否celery运行
    @return:
    """
    for _argv in sys.argv:
        if "celery" in _argv:
            return True
    return False


def is_shell():
    """
    判断当前环境是否shell运行
    @return:
    """
    for _argv in sys.argv:
        if "shell" in _argv:
            return True
    return False


# 后台任务 & 测试任务调用 ESB 接口不需要用户权限控制
if is_celery() or is_shell():

    def add_app_info_before_request(params):
        params["app_code"] = APP_ID
        params["app_secret"] = APP_TOKEN
        params["appenv"] = local.get_local_param("appenv")

        params["bkdata_authentication_method"] = "inner"
        params["bk_username"] = params.get("bk_username") or "admin"
        params["operator"] = params.get("operator") or "admin"

        return params


# 正常 WEB 请求所使用的函数
else:

    def add_app_info_before_request(params):
        params["bk_app_code"] = APP_ID
        params["bk_app_secret"] = APP_TOKEN
        params["appenv"] = local.get_local_param("appenv")

        # 通过 PIZZA 转调其他模块接口，现阶段认定为内部调用，走内部校验逻辑
        params["bkdata_authentication_method"] = "inner"
        params["bk_username"] = get_request_username()
        params["operator"] = get_request_username()
        return params
