# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

"""
Pizza 框架用到的中间件
"""
from django.http import HttpResponseRedirect
from django.middleware.locale import LocaleMiddleware
from opentelemetry.trace import get_current_span

from common import local
from common.base_utils import extract_parmas
from common.bklanguage import BkLanguage
from common.local import activate_request
from pizza.settings_default import BK_LANGUAGE_HEADER


class BkLocaleMiddleware(LocaleMiddleware):
    """
    此中间件处理http头中的blueking_language
    根据blueking_language取值控制api返回相应语言的信息
    """

    response_redirect_class = HttpResponseRedirect

    def __init__(self):
        super().__init__()

    def process_request(self, request):
        """
        请求之前根据http header决定bk用户使用哪种语言
        如果没有指定， 则根据django默认的国际化规则(使用http header accept_language)
        :param request:
        :return:
        """
        language_key = request.META.get(BK_LANGUAGE_HEADER, False)
        if not language_key:
            return super().process_request(request)

        # 设置BkLanguage语言
        BkLanguage.set_language(language_key, request)


class PizzaCommonMiddleware:
    """
    Pizza框架公共中间件，主要处理逻辑：

    1. 将请求信息记录至线程变量
    """

    def process_request(self, request):
        # 区分调用环境  ieod-内部版  clouds-混合云版

        # 1. 优先从头部获取   2. 若头部不存在则从参数内获取
        appenv = request.META.get("HTTP_APPENV", extract_parmas(request, local.LocalVariable.APPENV))
        # 3. 若都没有，则默认为ieod
        if not appenv:
            appenv = "ieod"

        local.set_local_param(local.LocalVariable.APPENV, appenv)
        try:
            request_id = f"{hex(get_current_span().get_span_context().trace_id)}".split("0x", 1)[1]
            if request_id == "0":
                raise
            activate_request(request, request_id)
        except Exception:  # pylint: disable=broad-except
            activate_request(request)
        return None

    def process_response(self, request, response):
        return response
