# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

"""
错误代码说明
{
    "code": "10003",
    "message": "xxd"
    "data": null,
    "result": false
}

业务相关可自行补充：
  如 TRT_BAD_REQUEST = "40002"
"""
import copy


class BaseException(Exception):
    pass


class APIError(BaseException):
    """
    API Error
    """

    def __init__(self, code):
        self.code = code
        BaseException.__init__(self, code.prompt)

    def __str__(self):
        return "<APIError {}[{}]: {}>".format(self.code.status, self.code.code, self.code.prompt)

    def format_prompt(self, prompt=None, replace=False, args=(), kwargs={}):
        """
        Using a customized prompt for this ErrorCode
        """
        self.code = copy.copy(self.code)
        if prompt:
            if replace:
                self.code.prompt = prompt
            else:
                self.code.prompt += ", %s" % prompt

        # Render prompt string
        if args:
            self.code.prompt = self.code.prompt % args
        if kwargs:
            self.code.prompt = self.code.prompt % kwargs
        return self


class ErrorCode:
    """
    Error Code class
    """

    def __init__(self, code_name, code, prompt, status=200):
        self.code_name = code_name
        self.code = code
        self.prompt = prompt
        self.status = status

    def as_dict(self):
        return {"result": False, "code": self.code, "data": None, "message": self.prompt}


class ErrorCodes:
    """
    错误代码规范
    错误代码为五位,
        如20001, 20002
    - ^1(.*)$: 1开头代表各业务返回的代码错误
    - ^2(.*)$: 2开头代表业务异常错误, 如Query模块中，20001为结果表不存在
    - 错误代码:
      * 50000: 默认错误代码
    """

    error_codes = (
        ErrorCode("ARGUMENT_ERROR", "10001", "参数错误"),
        # Error codes
        ErrorCode("USER_PERMISSION_DENIED", "40000", "用户权限不足"),
        ErrorCode("BAD_REQUEST", "40001", "非法请求"),
        # 通用错误编码，用于目前系统中没有错误code的情况
        ErrorCode("COMMON_ERROR", "50000", "失败请求,系统错误"),
        ErrorCode("EXTERNAL_ERROR", "50001", "请求外部接口错误"),
        ErrorCode("TRT_COMMON_ERROR", "10010", "TRT错误"),
        ErrorCode("MAPLE_COMMON_ERROR", "10020", "TRT错误"),
    )

    # Init dict
    _error_codes_dict = {}
    for error_code in error_codes:
        _error_codes_dict[error_code.code_name] = error_code

    def __getattr__(self, code_name):
        error_code = self._error_codes_dict[code_name]
        return APIError(error_code)


error_codes = ErrorCodes()


class CommonAPIError(APIError):
    """
    Shortcut for returning an error response
    """

    def __init__(self, message, error_code=None, status=None):
        """
        初始化一个常用的通用错误

        :param str message: 自定义的错误消息
        :param str error_code: 返回到相应的错误代码，默认50000
        """
        self.message = message
        code = error_codes.COMMON_ERROR.format_prompt(message, replace=True).code
        if error_code:
            code.code = error_code
        if status:
            code.status = status

        super().__init__(code)
