amqp==2.1.4
arrow==0.16.0
Django==1.11.20
django-filter==1.0.2
djangorestframework==3.4.4
gevent==20.9.0
greenlet==0.4.17
gunicorn==19.6.0
kafka-python==1.4.2
bk-kazoo==2.8.2
kombu==4.0.2
logstash-formatter==0.5.16
Mako==1.0.5
Markdown==2.6.6
MarkupSafe==0.23
meld3==1.0.2
pymysql==0.6.7
pyparsing==2.1.10
python-dateutil==2.7.0
pytz==2016.7
PyYAML==3.11
requests==2.22.0
supervisor==4.2.1
uWSGI==2.0.17
vine==1.1.3
numpy==1.11.2
cython==0.24.1
pandas==0.19.0
expiringdict==1.2.1
elasticsearch==6.3.1
thrift_connector==0.12
thrift==0.11.0
redis==2.10.5
crate==0.20.0
httplib2==0.19.0
celery==4.0.2
django-celery-results==1.0.1
django-celery-beat==1.0.1
python-consul==0.7.0
influxdb==4.1.0
python-etcd==0.4.5
redis-py-cluster==1.3.4
pika==0.11.0
pycrypto==2.6.1
fastavro==0.14.2
python-snappy==0.5.1
django-reversion==2.0.9
parquet==1.2
requests-toolbelt==0.8.0
enum34==1.1.6
pyquery==1.3.0
openpyxl==2.5.9
xlrd==1.1.0
backports.functools-lru-cache==1.5
chardet==3.0.4
jsonext==0.4.2
six==1.13.0
Werkzeug==0.14.1
tblib==1.3.2
sqlparse==0.2.4
JPype1==0.6.3
opentracing==2.3.0
django_opentracing==1.1.0
jaeger-client==4.3.0
opentracing_instrumentation==3.3.1
attrs==19.1.0
cached-property==1.5.1
portalocker==1.4.0
presto-python-client==0.7.0
pyhive[hive]==0.6.1
certifi==2019.9.11
idna==2.8
urllib3==1.25.6
django-redis==4.10.0
more-itertools==5.0.0
concurrent-log-handler==0.9.17
tornado==4.5.3
