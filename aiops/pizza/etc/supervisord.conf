[supervisord]
logfile = var/log/supervisord.log
logfile_maxbytes = 10MB
logfile_backups=10
loglevel = info
pidfile = var/run/supervisord.pid
childlogdir = var/log
nocleanup = true
nodaemon = false

[program:pizza]
command=bash -c "exec bin/pizza.sh"
process_name=pizza
redirect_stderr=true
stdout_logfile=var/log/proc-pizza.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=10
stderr_logfile=NONE

[supervisorctl]
serverurl = unix://var/log/pizza-supervisord.sock

[unix_http_server]
file = var/log/pizza-supervisord.sock
chmod = 0777

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
