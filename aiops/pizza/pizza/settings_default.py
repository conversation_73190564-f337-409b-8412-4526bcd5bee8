# """
# <PERSON><PERSON> is pleased to support the open source community by making BK-BASE 蓝鲸基础平台 available.
# Copyright (C) 2021 THL A29 Limited, a Tencent company.  All rights reserved.
# BK-BASE 蓝鲸基础平台 is licensed under the MIT License.
# License for BK-BASE 蓝鲸基础平台:
# --------------------------------------------------------------------
# Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated
# documentation files (the "Software"), to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software,
# and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
# The above copyright notice and this permission notice shall be included in all copies or substantial
# portions of the Software.
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
# LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
# NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
# SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
# """

"""
Django settings for pizza project.

Generated by 'django-admin startproject' using Django 1.8.11.

For more information on this file, see
https://docs.djangoproject.com/en/1.8/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.8/ref/settings/
"""

import logging.config

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
import os
import time

# 初始化支持多进程的FileLogger, 仅需import
import concurrent_log_handler  # noqa

from common import local
from common.base_crypt import BaseCrypt
from conf import dataapi_settings

APP_NAME = getattr(dataapi_settings, "APP_NAME")
RUN_VERSION = getattr(dataapi_settings, "RUN_VERSION")

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.8/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = getattr(dataapi_settings, "SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

# 加解密
DB_INSTANCE_KEY = getattr(dataapi_settings, "CRYPT_INSTANCE_KEY")
DB_CRYPT_ROOT_KEY = getattr(dataapi_settings, "DB_CRYPT_ROOT_KEY")
DB_CRYPT_ROOT_IV = getattr(dataapi_settings, "DB_CRYPT_ROOT_IV")
bk_crypt = BaseCrypt.instance(DB_CRYPT_ROOT_KEY, DB_CRYPT_ROOT_IV, DB_INSTANCE_KEY)

ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = (
    # 'django.contrib.admin',
    # 'django.contrib.auth',
    # 'django.contrib.contenttypes',
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
)

MIDDLEWARE_CLASSES = (
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    # 'django.middleware.csrf.CsrfViewMiddleware',
    # 'django.contrib.auth.middleware.AuthenticationMiddleware',
    # 'django.contrib.auth.middleware.SessionAuthenticationMiddleware',
    "django.contrib.messages.middleware.MessageMiddleware",
    # 'django.middleware.clickjacking.XFrameOptionsMiddleware',
    "django.middleware.security.SecurityMiddleware",
    "common.middlewares.BkLocaleMiddleware",
    "common.middlewares.PizzaCommonMiddleware",
    "common.auth.middlewares.AuthenticationMiddleware",
)

REST_FRAMEWORK = {
    "EXCEPTION_HANDLER": "common.base_utils.custom_exception_handler",
    "DEFAULT_THROTTLE_CLASSES": ("rest_framework.throttling.ScopedRateThrottle",),
    "DEFAULT_RENDERER_CLASSES": (
        "common.renderers.BKDataJSONRenderer",
        # 'rest_framework.renderers.JSONRenderer',
        # No BrowsableAPIRenderer, avoid 'get_serializer' error
        # 'rest_framework.renderers.BrowsableAPIRenderer',
    ),
    "DATETIME_FORMAT": "%Y-%m-%d %H:%M:%S",
    "UNICODE_JSON": False,
    "URL_FORMAT_OVERRIDE": "url_format",
}

ROOT_URLCONF = "pizza.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "pizza.wsgi.application"

# Database
# https://docs.djangoproject.com/en/1.8/ref/settings/#databases

# Use pymysql to replace MySQL-python
try:
    import pymysql

    pymysql.install_as_MySQLdb()
    # Patch version info to forcely pass Django client check
    setattr(pymysql, "version_info", (1, 2, 6, "final", 0))
except ImportError as e:
    raise ImportError("PyMySQL is not installed: %s" % e)

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "bkdata_basic",
        "USER": getattr(dataapi_settings, "CONFIG_DB_USER", "root"),
        "PASSWORD": getattr(dataapi_settings, "CONFIG_DB_PASSWORD", ""),
        "HOST": getattr(dataapi_settings, "CONFIG_DB_HOST", "127.0.0.1"),
        "PORT": getattr(dataapi_settings, "CONFIG_DB_PORT", 3306),
        "ENCRYPTED": False,
        "TEST": {
            "NAME": "bkdata_test",
        },
    }
}

# Internationalization
# https://docs.djangoproject.com/en/1.8/topics/i18n/

LANGUAGE_CODE = "zh-hans"
USE_I18N = True
USE_L10N = True
BK_LANGUAGE_HEADER = "HTTP_BLUEKING_LANGUAGE"

BK_LANGUAGE_EN = "en"
BK_LANGUAGE_CN = "zh-cn"
BK_LANGUAGE_ALL = "all"
BK_SUPPORTED_LANGUAGES = [BK_LANGUAGE_EN, BK_LANGUAGE_CN, BK_LANGUAGE_ALL]
LANGUAGES = (
    ("zh-hans", "中文"),
    ("en", "English"),
)
PIZZA_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOCALE_PATHS = [
    os.path.join(PIZZA_ROOT, APP_NAME, "locale"),
    os.path.join(PIZZA_ROOT, "locale"),
]

TIME_ZONE = "Etc/GMT%+d" % ((time.altzone if time.daylight else time.timezone) / 3600)

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.8/howto/static-files/
STATIC_URL = "/static/"

# Logging config
LOGGER_LEVEL = getattr(dataapi_settings, "LOGGER_LEVEL", "INFO")
LOG_DIR = getattr(dataapi_settings, "LOG_DIR", None)
# logging
if not LOG_DIR:
    LOG_DIR = os.path.join(BASE_DIR, "logs", "bkdata", "{}api".format(APP_NAME))
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

LOG_MAX_BYTES = getattr(dataapi_settings, "LOG_MAX_BYTES", 524288000)  # 500M
LOG_BACKUP_COUNT = getattr(dataapi_settings, "LOG_BACKUP_COUNT", 5)
LOG_CLASS = "logging.handlers.ConcurrentRotatingFileHandler"


class BKBaseLogRecord(logging.LogRecord):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.request_id = local.get_request_id()


logging.setLogRecordFactory(BKBaseLogRecord)


def get_loggings(log_level):
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "verbose": {
                "class": "dispatching.utils.logging.RichExceptionFormatter",
                "format": (
                    "%(name)40s\t%(levelname)5s\t%(asctime)10s\t%(module)10s\t%(funcName)20s\t%(lineno)3s:\t"
                    "%(message)s\t%(request_id)s\t%(thread)5s\t%(process)5s"
                ),
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "class": "dispatching.utils.logging.RichExceptionJsonFormatter",
            },
            "simple": {"format": "%(message)s"},
        },
        "handlers": {
            "null": {
                "level": "DEBUG",
                "class": "logging.NullHandler",
            },
            "console": {"level": "DEBUG", "class": "logging.StreamHandler", "formatter": "verbose"},
            "root": {
                "class": LOG_CLASS,
                "formatter": "json",
                "filename": os.path.join(LOG_DIR, "sys.log"),
                "maxBytes": LOG_MAX_BYTES,
                "backupCount": LOG_BACKUP_COUNT,
            },
            "api": {
                "class": LOG_CLASS,
                "formatter": "simple",
                "filename": os.path.join(LOG_DIR, "api.log"),
                "maxBytes": LOG_MAX_BYTES,
                "backupCount": LOG_BACKUP_COUNT,
            },
        },
        "loggers": {
            "django": {
                "handlers": ["null"],
                "level": "INFO",
                "propagate": True,
            },
            "django.request": {
                "handlers": ["root"],
                "level": "ERROR",
                "propagate": True,
            },
            # Logging config for api
            "api": {
                "handlers": ["api"],
                "level": log_level,
                "propagate": False,
            },
        },
        "root": {"level": log_level, "handlers": ["root"]},
    }


LOGGING = get_loggings(LOGGER_LEVEL)
RUN_MODE = os.environ.get("RUN_MODE", "DEVELOP")
logging.getLogger("pika").setLevel(logging.CRITICAL)
logging.getLogger("kazoo").setLevel(logging.INFO)
logging.getLogger("py4j").setLevel(logging.INFO)
logging.getLogger("apscheduler").setLevel(logging.WARNING)

# Cache

CACHES = {
    "db": {
        "BACKEND": "django.core.cache.backends.db.DatabaseCache",
        "LOCATION": "django_cache",
    },
    "locmem": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
    },
}

CACHES["default"] = CACHES["locmem"]

# OpenTracing配置
DO_TRACE = getattr(dataapi_settings, "DO_TRACE", False)
TRACING_SAMPLE_RATE = getattr(dataapi_settings, "TRACING_SAMPLE_RATE", 1)

MULTI_GEOG_AREA = getattr(dataapi_settings, "MULTI_GEOG_AREA", False)

META_ACCESS_RPC_ENDPOINT = "http://{ATLAS_RPC_HOST}:{ATLAS_RPC_PORT}/jsonrpc/2.0/".format(
    ATLAS_RPC_HOST=getattr(dataapi_settings, "ATLAS_RPC_HOST", "127.0.0.1"),
    ATLAS_RPC_PORT=getattr(dataapi_settings, "ATLAS_RPC_PORT", "5000"),
)

META_ACCESS_RPC_ENDPOINT_TEMPLATE = "http://{ATLAS_RPC_HOST}:{ATLAS_RPC_PORT}/jsonrpc/2.0/"

META_ACCESS_RPC_TIMEOUT = getattr(dataapi_settings, "META_ACCESS_RPC_TIMEOUT", 120)

ZK_ADDR = getattr(dataapi_settings, "ZK_ADDR", "127.0.0.1:2181")

ZK_PATH = getattr(dataapi_settings, "ZK_PATH", "/metadata_access_service")

METADATA_HA = getattr(dataapi_settings, "METADATA_HA", True)

METADATA_HA_WAIT_TIME = getattr(dataapi_settings, "METADATA_HA_WAIT_TIME", 2)

TDW_STD_BIZ_ID = getattr(dataapi_settings, "TDW_STD_BIZ_ID", 5000139)

ENABLE_QUERY_STORAGE_LIST = [
    "ignite",
    "druid",
    "mysql",
    "tdw",
    "tsdb",
    "es",
    "tpg",
    "hdfs",
    "postgresql",
    "presto",
    "tspider",
    "hermes",
]

FORCE_USER_VERIFY = True
